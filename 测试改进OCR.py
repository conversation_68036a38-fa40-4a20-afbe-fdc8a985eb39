#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的OCR识别效果
"""

import os
import easyocr
import fitz
import cv2
import numpy as np

def enhance_image_for_ocr(image_path):
    """图像预处理以提高OCR识别率"""
    try:
        # 读取图像
        img = cv2.imread(image_path)
        if img is None:
            return image_path
        
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 适度放大图像提高分辨率（避免内存问题）
        height, width = gray.shape
        scale_factor = 3.0  # 提高到3倍，但使用更好的预处理
        high_res = cv2.resize(gray, (int(width * scale_factor), int(height * scale_factor)), interpolation=cv2.INTER_CUBIC)
        
        # 多步骤图像增强
        # 1. 对比度和亮度增强
        enhanced = cv2.convertScaleAbs(high_res, alpha=2.0, beta=40)
        
        # 2. 自适应直方图均衡化（提高文字清晰度）
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(enhanced)
        
        # 3. 去噪处理
        enhanced = cv2.fastNlMeansDenoising(enhanced)
        
        # 保存增强后的图像
        enhanced_path = "temp_enhanced_test.png"
        cv2.imwrite(enhanced_path, enhanced)
        
        print(f"图像预处理完成: {enhanced_path}")
        return enhanced_path
    except Exception as e:
        print(f"图像预处理失败: {str(e)}")
        return image_path

def correct_ocr_errors(text):
    """OCR错误修正"""
    corrections = {
        # 公司名称常见错误
        '樊捷': '奥捷', '樊源': '奥源', '来亍': '来宁', '锦阴': '锦阳',
        '揭阴': '揭阳', '广氽': '广东', '夭美': '天美', '順洋': '顺洋',
        
        # 发票类型错误
        '僧值税': '增值税', '橹徒税': '增值税', '增僮税': '增值税',
        
        # 金额符号错误
        '羊': '￥', '芏': '￥',
    }
    
    corrected_text = text
    for error, correct in corrections.items():
        if error in corrected_text:
            corrected_text = corrected_text.replace(error, correct)
    
    return corrected_text

def test_improved_ocr():
    """测试改进的OCR识别"""
    try:
        # 查找奥捷发票
        test_pdf = None
        for root, dirs, files in os.walk('.'):
            for file in files:
                if '奥捷' in file and file.lower().endswith('.pdf'):
                    test_pdf = os.path.join(root, file)
                    break
            if test_pdf:
                break
        
        if not test_pdf:
            print("未找到奥捷发票文件")
            return
        
        print(f"测试PDF文件: {test_pdf}")
        
        # 初始化OCR
        print("正在初始化EasyOCR...")
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        print("EasyOCR初始化完成！")
        
        # PDF转图片
        doc = fitz.open(test_pdf)
        page = doc[0]
        mat = fitz.Matrix(4.0, 4.0)  # 4倍缩放
        pix = page.get_pixmap(matrix=mat)
        
        original_image = "original_test_ocr.png"
        pix.save(original_image)
        doc.close()
        
        print("PDF转换完成，开始图像预处理...")
        
        # 图像预处理
        enhanced_image = enhance_image_for_ocr(original_image)
        
        print("开始OCR识别...")
        
        # 对比原始图像和增强图像的识别效果
        print("\n" + "="*60)
        print("原始图像识别结果:")
        print("="*60)
        
        original_result = reader.readtext(original_image, detail=1)
        target_keywords = ['奥捷', '樊捷', '增值税', '专用发票']
        
        for i, (bbox, text, confidence) in enumerate(original_result):
            if any(keyword in text for keyword in target_keywords):
                corrected = correct_ocr_errors(text)
                print(f"{i+1:2d}. 置信度:{confidence:.3f} | 原始: '{text}' | 修正: '{corrected}'")
        
        print("\n" + "="*60)
        print("增强图像识别结果:")
        print("="*60)
        
        enhanced_result = reader.readtext(enhanced_image, detail=1)
        
        for i, (bbox, text, confidence) in enumerate(enhanced_result):
            if any(keyword in text for keyword in target_keywords):
                corrected = correct_ocr_errors(text)
                print(f"{i+1:2d}. 置信度:{confidence:.3f} | 原始: '{text}' | 修正: '{corrected}'")
        
        # 查找关键信息
        print("\n" + "="*60)
        print("关键信息识别对比:")
        print("="*60)
        
        def find_company_name(results):
            for bbox, text, confidence in results:
                corrected = correct_ocr_errors(text)
                if '奥捷' in corrected and '公司' in corrected:
                    return corrected, confidence
            return None, 0
        
        def find_invoice_type(results):
            for bbox, text, confidence in results:
                corrected = correct_ocr_errors(text)
                if '增值税' in corrected and '发票' in corrected:
                    return corrected, confidence
            return None, 0
        
        # 原始图像结果
        orig_company, orig_conf = find_company_name(original_result)
        orig_invoice, orig_inv_conf = find_invoice_type(original_result)
        
        # 增强图像结果
        enh_company, enh_conf = find_company_name(enhanced_result)
        enh_invoice, enh_inv_conf = find_invoice_type(enhanced_result)
        
        print("公司名称识别:")
        print(f"  原始图像: {orig_company} (置信度: {orig_conf:.3f})")
        print(f"  增强图像: {enh_company} (置信度: {enh_conf:.3f})")
        
        print("\n发票类型识别:")
        print(f"  原始图像: {orig_invoice} (置信度: {orig_inv_conf:.3f})")
        print(f"  增强图像: {enh_invoice} (置信度: {enh_inv_conf:.3f})")
        
        # 清理临时文件
        for temp_file in [original_image, enhanced_image]:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except:
                pass
        
        print("\n临时文件已清理")
        
        # 总结改进效果
        print("\n" + "="*60)
        print("改进效果总结:")
        print("="*60)
        
        if enh_company and '奥捷' in enh_company:
            print("✅ 公司名称识别成功：能够正确识别'奥捷'")
        else:
            print("❌ 公司名称识别仍有问题")
        
        if enh_invoice and '增值税专用发票' in enh_invoice:
            print("✅ 发票类型识别成功：能够正确识别'增值税专用发票'")
        else:
            print("❌ 发票类型识别仍有问题")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_improved_ocr()
    input("\n按回车键退出...")
