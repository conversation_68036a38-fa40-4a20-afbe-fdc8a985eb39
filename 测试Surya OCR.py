#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Surya OCR识别效果
"""

import os
from PIL import Image
import fitz
from surya.recognition import RecognitionPredictor
from surya.detection import DetectionPredictor

def test_surya_ocr():
    """测试Surya OCR识别效果"""
    try:
        print("测试Surya OCR识别效果")
        print("="*50)
        
        # 查找测试PDF文件
        test_pdf = None
        for root, dirs, files in os.walk('.'):
            for file in files:
                if '奥捷' in file and file.lower().endswith('.pdf'):
                    test_pdf = os.path.join(root, file)
                    break
            if test_pdf:
                break
        
        if not test_pdf:
            print("未找到奥捷发票文件进行测试")
            return
        
        print(f"测试文件: {test_pdf}")
        
        # PDF转图片
        print("转换PDF为图片...")
        doc = fitz.open(test_pdf)
        page = doc[0]
        mat = fitz.Matrix(3.0, 3.0)  # 3倍缩放
        pix = page.get_pixmap(matrix=mat)
        
        test_image = "test_surya_image.png"
        pix.save(test_image)
        doc.close()
        
        print(f"图片保存为: {test_image}")
        
        # 初始化Surya OCR
        print("\n初始化Surya OCR...")
        detection_predictor = DetectionPredictor()
        recognition_predictor = RecognitionPredictor()
        print("Surya OCR初始化完成！")
        
        # 加载图像
        image = Image.open(test_image)
        
        # 运行OCR识别
        print("\n开始OCR识别...")
        predictions = recognition_predictor([image], det_predictor=detection_predictor)
        
        print(f"识别完成！共识别到 {len(predictions)} 页")
        
        if predictions and len(predictions) > 0:
            page_prediction = predictions[0]
            print(f"第一页识别到 {len(page_prediction.text_lines)} 行文本")
            
            # 显示识别结果
            print("\n识别结果:")
            print("-" * 60)
            
            target_keywords = ['奥捷', '增值税', '专用发票', '13%', '税率']
            found_keywords = []
            
            for i, text_line in enumerate(page_prediction.text_lines):
                text = text_line.text
                confidence = text_line.confidence
                
                # 检查是否包含目标关键词
                contains_keyword = any(keyword in text for keyword in target_keywords)
                
                if contains_keyword or confidence > 0.8:  # 显示高置信度或包含关键词的文本
                    print(f"{i+1:2d}. 置信度:{confidence:.3f} | 文本: '{text}'")
                    
                    # 记录找到的关键词
                    for keyword in target_keywords:
                        if keyword in text and keyword not in found_keywords:
                            found_keywords.append(keyword)
            
            print("-" * 60)
            print(f"找到的关键词: {found_keywords}")
            
            # 评估识别效果
            print("\n识别效果评估:")
            if '奥捷' in found_keywords:
                print("✅ 公司名称识别: 成功识别'奥捷'")
            else:
                print("❌ 公司名称识别: 未能识别'奥捷'")
            
            if '增值税' in found_keywords and '专用发票' in found_keywords:
                print("✅ 发票类型识别: 成功识别'增值税专用发票'")
            elif '增值税' in found_keywords:
                print("⚠️ 发票类型识别: 部分识别'增值税'")
            else:
                print("❌ 发票类型识别: 未能识别发票类型")
            
            if '13%' in found_keywords or '税率' in found_keywords:
                print("✅ 税率识别: 成功识别税率相关信息")
            else:
                print("❌ 税率识别: 未能识别税率信息")
        
        # 清理临时文件
        try:
            os.remove(test_image)
            print(f"\n临时文件已清理: {test_image}")
        except:
            pass
        
        print("\n测试完成！")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_surya_ocr()
    input("\n按回车键退出...")
