# 发票处理程序 - 本地版本

这是一个使用本地OCR技术（EasyOCR）替代百度API的发票处理程序，可以在无网络环境下运行。

## 主要改进

- ✅ 使用EasyOCR替代百度API，实现完全本地化处理
- ✅ 支持中英文发票识别
- ✅ 无需网络连接即可运行
- ✅ 配置了虚拟环境，避免依赖冲突
- ✅ 改进了发票信息提取算法，提高识别准确率

## 环境要求

- Python 3.8+
- Windows 系统
- 至少4GB内存（推荐8GB）
- 约2GB磁盘空间（用于模型文件）

## 安装和配置

### 1. 虚拟环境已配置完成

项目已经配置好虚拟环境，位于 `venv` 文件夹中。

### 2. 依赖包已安装

所有必要的依赖包已经安装在虚拟环境中：
- easyocr - 本地OCR识别引擎
- pdf2image - PDF转图片
- pywin32 - Windows COM接口
- pillow - 图像处理
- opencv-python - 计算机视觉库
- torch/torchvision - 深度学习框架

## 使用方法

### 方法1：使用启动脚本（推荐）

双击运行 `启动发票处理.bat` 文件

### 方法2：手动启动

1. 打开命令提示符
2. 进入项目目录
3. 激活虚拟环境：
   ```
   venv\Scripts\activate
   ```
4. 运行程序：
   ```
   python "fppl-21.尝试替代百度识别.py"
   ```

## 程序功能

1. **发票识别**：自动识别发票中的关键信息
   - 发票类型（增值税专用发票/普通发票）
   - 金额（小写）
   - 价税合计
   - 税率
   - 税额
   - 销售方名称
   - 备注信息

2. **Excel处理**：
   - 读取导出清单
   - 匹配关键词
   - 自动填充发票信息
   - 生成报销表

3. **文件支持**：
   - PDF发票文件
   - 图片格式发票（JPG、PNG等）

## 测试

运行测试脚本验证EasyOCR是否正常工作：
```
venv\Scripts\python 测试EasyOCR.py
```

## 注意事项

1. **首次运行**：程序首次启动时会下载OCR模型文件（约200MB），请确保网络连接
2. **性能**：使用CPU进行OCR识别，处理速度相对较慢，请耐心等待
3. **准确率**：本地OCR的准确率可能略低于云端API，建议处理后检查结果
4. **文件路径**：请使用绝对路径，避免相对路径导致的问题

## 故障排除

### 常见问题

1. **模块导入错误**：确保在虚拟环境中运行程序
2. **OCR识别失败**：检查图片质量，确保文字清晰
3. **PDF转换失败**：确保Poppler路径正确配置

### 获取帮助

如果遇到问题，请检查：
1. 虚拟环境是否正确激活
2. 所有依赖包是否正确安装
3. 文件路径是否正确
4. 图片/PDF文件是否损坏

## 版本信息

- 版本：2.0（本地化版本）
- 更新日期：2025年6月
- OCR引擎：EasyOCR 1.7.2
- Python版本：3.13+
