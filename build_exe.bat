@echo off
chcp 65001 >nul
echo Starting to build invoice processing program...
echo.

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Check if virtual environment is activated
if "%VIRTUAL_ENV%"=="" (
    echo Error: Cannot activate virtual environment
    pause
    exit /b 1
)

echo Virtual environment activated: %VIRTUAL_ENV%
echo.

echo Building with PyInstaller...
echo This may take several minutes, please wait...
echo.

REM Build with PyInstaller as single exe file
pyinstaller --onefile --windowed --name "InvoiceProcessor" --hidden-import=easyocr --hidden-import=fitz --hidden-import=cv2 --hidden-import=PIL --hidden-import=torch --hidden-import=numpy --hidden-import=requests --hidden-import=win32com.client --hidden-import=tkinter --hidden-import=tkinter.filedialog --hidden-import=tkinter.ttk --hidden-import=tkinter.messagebox --exclude-module=matplotlib --exclude-module=scipy --exclude-module=pandas "fppl-21.尝试替代百度识别.py"

if errorlevel 1 (
    echo.
    echo Build failed, please check error messages
    pause
    exit /b 1
)

echo.
echo Build completed!
echo Executable file location: dist\InvoiceProcessor.exe
echo.

REM Check if file exists
if exist "dist\InvoiceProcessor.exe" (
    echo File information:
    dir "dist\InvoiceProcessor.exe" | find ".exe"
    echo.
    echo Build successful!
    echo.
    echo Usage instructions:
    echo 1. Copy "InvoiceProcessor.exe" to target computer
    echo 2. First run will download OCR models automatically (requires internet)
    echo 3. After download, can be used offline
    echo.
) else (
    echo Warning: Generated exe file not found
)

pause
