#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试EasyOCR是否正常工作
"""

import easyocr
import os

def test_easyocr():
    """测试EasyOCR基本功能"""
    print("开始测试EasyOCR...")
    
    try:
        # 初始化EasyOCR
        print("正在初始化EasyOCR...")
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        print("EasyOCR初始化成功！")
        
        # 检查是否有测试图片
        test_images = []
        for ext in ['.jpg', '.jpeg', '.png', '.pdf']:
            for file in os.listdir('.'):
                if file.lower().endswith(ext):
                    test_images.append(file)
        
        if test_images:
            print(f"找到测试图片: {test_images[:3]}")  # 只显示前3个
            
            # 测试第一个图片
            test_image = test_images[0]
            print(f"正在测试图片: {test_image}")
            
            result = reader.readtext(test_image)
            
            print(f"识别结果 ({len(result)} 个文本区域):")
            for i, (bbox, text, confidence) in enumerate(result[:5]):  # 只显示前5个结果
                print(f"  {i+1}. 文本: '{text}' (置信度: {confidence:.2f})")
        else:
            print("未找到测试图片文件")
            print("请在当前目录放置一些图片文件(.jpg, .png等)进行测试")
        
        print("EasyOCR测试完成！")
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_easyocr()
    input("按回车键退出...")
