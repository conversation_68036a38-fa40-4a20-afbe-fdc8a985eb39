#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证每张发票的识别结果
"""

import os
import sys

# 添加当前目录到路径
sys.path.append('.')

def test_each_invoice():
    """测试每张发票的识别结果"""
    try:
        print("验证每张发票的识别结果")
        print("="*60)
        
        # 发票文件夹
        invoice_folder = r"D:\vscode project\发票处理\1\202501顺洋、来宁、锦阳、奥捷、奥源-5张"
        
        if not os.path.exists(invoice_folder):
            print(f"❌ 发票文件夹不存在: {invoice_folder}")
            return
        
        # 查找PDF文件
        pdf_files = []
        for file in os.listdir(invoice_folder):
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(invoice_folder, file))
        
        if not pdf_files:
            print("❌ 未找到PDF文件")
            return
        
        print(f"找到 {len(pdf_files)} 个PDF文件")
        
        # 预期的发票金额（从百度API结果）
        expected_amounts = {
            '锦阳': {'K': 25458.17, 'L': 22529.38, 'N': 2928.79},
            '顺洋': {'K': '待确认', 'L': '待确认', 'N': '待确认'},
            '来宁': {'K': '待确认', 'L': '待确认', 'N': '待确认'},
            '奥捷': {'K': '待确认', 'L': '待确认', 'N': '待确认'},
            '奥源': {'K': '待确认', 'L': '待确认', 'N': '待确认'}
        }
        
        # 测试每个PDF文件
        for i, pdf_path in enumerate(pdf_files, 1):
            print(f"\n{'='*60}")
            print(f"测试文件 {i}/{len(pdf_files)}: {os.path.basename(pdf_path)}")
            print(f"{'='*60}")
            
            # 从文件名推断关键词
            filename = os.path.basename(pdf_path)
            detected_keyword = None
            for keyword in ['锦阳', '顺洋', '来宁', '奥捷', '奥源']:
                if keyword in filename:
                    detected_keyword = keyword
                    break
            
            if detected_keyword:
                print(f"从文件名检测到关键词: {detected_keyword}")
                expected = expected_amounts.get(detected_keyword, {})
                if expected.get('K') != '待确认':
                    print(f"预期金额 - K列: {expected['K']}, L列: {expected['L']}, N列: {expected['N']}")
            else:
                print("未能从文件名检测到关键词")
            
            # 使用EasyOCR进行快速测试（因为Surya太慢）
            try:
                from fppl_21_尝试替代百度识别 import recognize_invoice_easyocr
                
                print("使用EasyOCR进行识别...")
                result = recognize_invoice_easyocr(pdf_path)
                
                if result:
                    print("\n✅ EasyOCR识别结果:")
                    print("-" * 40)
                    
                    # 检查关键字段
                    fields_to_check = [
                        ('发票类型', 'InvoiceTypeOrg'),
                        ('小写金额', 'AmountInFiguers'),
                        ('价税合计', 'TotalAmount'),
                        ('商品税率', 'CommodityTaxRate'),
                        ('税额', 'TotalTax'),
                        ('销售方名称', 'SellerName'),
                    ]
                    
                    for field_name, field_key in fields_to_check:
                        value = result.get(field_key, '')
                        print(f"  {field_name}: {value}")
                    
                    # 检查关键词匹配
                    seller_name = result.get('SellerName', '')
                    keywords = ['顺洋', '来宁', '锦阳', '奥捷', '奥源']
                    matched_keywords = [kw for kw in keywords if kw in seller_name]
                    
                    if matched_keywords:
                        print(f"\n✅ 匹配的关键词: {matched_keywords}")
                        
                        # 对比预期金额
                        if detected_keyword and detected_keyword in matched_keywords:
                            expected = expected_amounts.get(detected_keyword, {})
                            actual_total = result.get('TotalAmount', '')
                            actual_amount = result.get('AmountInFiguers', '')
                            actual_tax = result.get('TotalTax', '')
                            
                            print(f"\n金额对比 (关键词: {detected_keyword}):")
                            if expected.get('K') != '待确认':
                                try:
                                    if actual_total and abs(float(actual_total) - expected['K']) < 0.01:
                                        print(f"  ✅ 价税合计匹配: {actual_total} ≈ {expected['K']}")
                                    else:
                                        print(f"  ❌ 价税合计不匹配: {actual_total} ≠ {expected['K']}")
                                    
                                    if actual_amount and abs(float(actual_amount) - expected['L']) < 0.01:
                                        print(f"  ✅ 不含税金额匹配: {actual_amount} ≈ {expected['L']}")
                                    else:
                                        print(f"  ❌ 不含税金额不匹配: {actual_amount} ≠ {expected['L']}")
                                    
                                    if actual_tax and abs(float(actual_tax) - expected['N']) < 0.01:
                                        print(f"  ✅ 税额匹配: {actual_tax} ≈ {expected['N']}")
                                    else:
                                        print(f"  ❌ 税额不匹配: {actual_tax} ≠ {expected['N']}")
                                except ValueError as e:
                                    print(f"  ❌ 金额格式错误: {e}")
                            else:
                                print(f"  📝 记录实际金额: K={actual_total}, L={actual_amount}, N={actual_tax}")
                        
                    else:
                        print(f"❌ 未匹配到关键词，销售方名称: {seller_name}")
                
                else:
                    print("❌ EasyOCR识别失败")
                    
            except ImportError:
                print("❌ 无法导入EasyOCR模块")
            except Exception as e:
                print(f"❌ 识别过程出错: {str(e)}")
        
        print(f"\n{'='*60}")
        print("验证完成")
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_each_invoice()
    input("\n按回车键退出...")
