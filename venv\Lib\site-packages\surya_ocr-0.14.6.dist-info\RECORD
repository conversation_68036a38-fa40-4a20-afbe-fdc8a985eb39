../../Scripts/surya_detect.exe,sha256=IF_Vw3MxEqlj7czpQqdXRKUrUqrUR87T7nZOt3K9hN0,108438
../../Scripts/surya_gui.exe,sha256=CUQmOKCv5CO_Gpxe3AGnCXgbY0lg0hCw47TyFfVB13U,108448
../../Scripts/surya_latex_ocr.exe,sha256=XHooNFKt606SKZzTUnOgeuz4TU0RjpqsaDMnPN7CUAM,108432
../../Scripts/surya_layout.exe,sha256=JEu8ilZHQJ81loKFZQzPKBhscLD8G0VdUaICIrIFOCI,108444
../../Scripts/surya_ocr.exe,sha256=VRvqo0EA3mly4y3Kh71vgAiChrk0_YVg9SrNkzW-_4Q,108429
../../Scripts/surya_table.exe,sha256=4sY3o82A8RLg4y3zEd2JadPvUX2sBgz-ThpUzllpB2I,108456
../../Scripts/texify_gui.exe,sha256=IUVetYTemPvJCbsbSoqyDh37zc0OK-RTg2MTD14uOtc,108439
surya/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
surya/__pycache__/__init__.cpython-313.pyc,,
surya/__pycache__/logging.cpython-313.pyc,,
surya/__pycache__/models.cpython-313.pyc,,
surya/__pycache__/settings.cpython-313.pyc,,
surya/common/__init__.py,sha256=ajz1GSNU9xYVrFEDSz6Xwg7amWQ_yvW75tQa1ZvRIWc,3
surya/common/__pycache__/__init__.cpython-313.pyc,,
surya/common/__pycache__/load.cpython-313.pyc,,
surya/common/__pycache__/polygon.cpython-313.pyc,,
surya/common/__pycache__/predictor.cpython-313.pyc,,
surya/common/__pycache__/s3.cpython-313.pyc,,
surya/common/__pycache__/util.cpython-313.pyc,,
surya/common/adetr/__pycache__/decoder.cpython-313.pyc,,
surya/common/adetr/decoder.py,sha256=vNlM0Upp8qdInDM0lDH-cKYxCUHFSfB8_xT6Af848Zo,29067
surya/common/donut/__pycache__/encoder.cpython-313.pyc,,
surya/common/donut/__pycache__/processor.cpython-313.pyc,,
surya/common/donut/encoder.py,sha256=GtUjnhtpP3uamwcJjtjOcw08VdHAhgQGxVKxkHLgreg,36537
surya/common/donut/processor.py,sha256=r5x_JLzQEKYeFb60uVj-9Y641YzZHOEktvy0zAMwOXs,6496
surya/common/load.py,sha256=dKQhGnUjWvuXSjoZD3R7lcxYRlwu7DV54KLHH_bC470,644
surya/common/polygon.py,sha256=Q5I8hkWOcYA72Gmeuv0h2DAYBEM7-qK6bye4ZD3MRNE,7134
surya/common/predictor.py,sha256=j8e1dAnqZQ6Klk0p9NdjxHRGubA98oUUYA6BaH4hhuQ,1840
surya/common/s3.py,sha256=XLaQMwUOXHSTxjEakkEADkFJYLcNmUOXlTXsPOf9dB8,5437
surya/common/surya/__init__.py,sha256=JEa61rJOUeuQnVSQhXImTjw6owEOlwFHvUGEYS6E0tw,12114
surya/common/surya/__pycache__/__init__.cpython-313.pyc,,
surya/common/surya/__pycache__/config.cpython-313.pyc,,
surya/common/surya/__pycache__/flash_attn_utils.cpython-313.pyc,,
surya/common/surya/__pycache__/schema.cpython-313.pyc,,
surya/common/surya/config.py,sha256=fJ43JSM_VTGDEmd1BhmcpCTKwcANQHd3GdBEK69U3Qo,2618
surya/common/surya/decoder/__init__.py,sha256=91tbsrX4L4ox0MGFDKU8zcDlDs5jFJMxAfERqLQWlFc,25690
surya/common/surya/decoder/__pycache__/__init__.cpython-313.pyc,,
surya/common/surya/decoder/__pycache__/config.cpython-313.pyc,,
surya/common/surya/decoder/config.py,sha256=U_wbP9ncXy3RcmV1pehqM-Wlqg0gCl3SN16H0ClXsjA,3105
surya/common/surya/embedder/__init__.py,sha256=PMYew8u1dZjQKNMVfSr9kL9Af1yBW2soI4LIx1kiuhk,348
surya/common/surya/embedder/__pycache__/__init__.cpython-313.pyc,,
surya/common/surya/encoder/__init__.py,sha256=qh7dB1J0xUJBJBcJo-7vpxaoQFdCMDCcN-XP36bGw9Q,27577
surya/common/surya/encoder/__pycache__/__init__.cpython-313.pyc,,
surya/common/surya/encoder/__pycache__/config.cpython-313.pyc,,
surya/common/surya/encoder/config.py,sha256=_jZ7V2-zuX2g2-jHZEFexik6UkeSZKSkKue4ue5t7M8,1610
surya/common/surya/flash_attn_utils.py,sha256=A5dejj5wyq_0uwYixTzcG5gtbmyhnY6fKRLIbrpAdns,8104
surya/common/surya/processor/__init__.py,sha256=dRRzrvRJkqU6w1Vl3ndOajr3hwwOHA1fDt0NRzlhlBU,15681
surya/common/surya/processor/__pycache__/__init__.cpython-313.pyc,,
surya/common/surya/processor/__pycache__/schema.cpython-313.pyc,,
surya/common/surya/processor/__pycache__/tokenizer.cpython-313.pyc,,
surya/common/surya/processor/schema.py,sha256=vERRN6pgk0W6IE9Ggo8ECJm2-NYBNCq9TgqGOIUgCY4,714
surya/common/surya/processor/tokenizer.py,sha256=ZbAqWmvR46nczew2ETdVX3ShY-HZaIFLuciMKhCSpgY,10995
surya/common/surya/schema.py,sha256=sRHsZbY-MlLlQvm2yeHy2tov3jyXwGRfRNk1KhNkdKM,264
surya/common/util.py,sha256=ip6lhr4JVp9nGPmHlCpDXT65kMjnqVTsWnx1DkTCUKU,2633
surya/debug/__pycache__/draw.cpython-313.pyc,,
surya/debug/__pycache__/fonts.cpython-313.pyc,,
surya/debug/__pycache__/render_html.cpython-313.pyc,,
surya/debug/__pycache__/text.cpython-313.pyc,,
surya/debug/draw.py,sha256=dMZ6sF45F1zxoQz0MvVB0c7dSxObnmt0YSpxTQq2wQg,1906
surya/debug/fonts.py,sha256=YhrN49-a-3zRZwUnkfkcm3OG80Y8aFeKaCjElye5l8E,853
surya/debug/katex.js,sha256=uThX0C9kEzj0T_-iR6N5T2b5f1bRLkNYFACAZ3ZInQo,2017
surya/debug/render_html.py,sha256=IWQVjoBbk_AN1uCX_BaurkPXLWG7DpMToyiWST2NZdg,2613
surya/debug/text.py,sha256=ejwHonWGVPwdrwM2zG9D6Q0S1edXyNLkXlHqlU1Uaog,3140
surya/detection/__init__.py,sha256=qO0E2AJ60oskpdgGeYZIJzHQrrL3PHw60kUc8l11oQQ,6256
surya/detection/__pycache__/__init__.cpython-313.pyc,,
surya/detection/__pycache__/affinity.cpython-313.pyc,,
surya/detection/__pycache__/heatmap.cpython-313.pyc,,
surya/detection/__pycache__/loader.cpython-313.pyc,,
surya/detection/__pycache__/parallel.cpython-313.pyc,,
surya/detection/__pycache__/processor.cpython-313.pyc,,
surya/detection/__pycache__/schema.cpython-313.pyc,,
surya/detection/__pycache__/util.cpython-313.pyc,,
surya/detection/affinity.py,sha256=tYEX255_jgtpfgQ6DBabQ_c1vdbuqR8YYgzaJ0WGsoE,5581
surya/detection/heatmap.py,sha256=7SiTXbZgjF0zAtH1VAHqqXK-ILi0fuGaaavz5inEK0k,6174
surya/detection/loader.py,sha256=JAw-c47WWpK7iDkIhdXUe8pRd3WHxLKCxgE8Zmwxi68,2223
surya/detection/model/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
surya/detection/model/__pycache__/__init__.cpython-313.pyc,,
surya/detection/model/__pycache__/config.cpython-313.pyc,,
surya/detection/model/__pycache__/encoderdecoder.cpython-313.pyc,,
surya/detection/model/config.py,sha256=pkrpmDWHV8icF2WP-OX5Yw9iC9nijdkjIE--SFEdZzQ,1645
surya/detection/model/encoderdecoder.py,sha256=iT1xXaE3PlrKyRE2dztMRuP4q4srFvs4PX0eKs6rD48,24635
surya/detection/parallel.py,sha256=LSPAjvINuAeyqh3qk7c8ro1JbIYP7TPIEYZE0bVIpqE,414
surya/detection/processor.py,sha256=XKGtQCApzaU0VBmGghCMACyhOm2PNgtXVFFYGXGfVvQ,13867
surya/detection/schema.py,sha256=AuFYVS5bfgovaI0OmBs6XZMMG0ZlbSKmXHbx_cy656I,380
surya/detection/util.py,sha256=Xfet07drLL2siOxrrApwBKFTUjXrC4jZgWI9jfI12Po,1250
surya/input/__pycache__/load.cpython-313.pyc,,
surya/input/__pycache__/processing.cpython-313.pyc,,
surya/input/load.py,sha256=b9oNnezGZ2w6b3iGUSZ3Oo4WGs6GvQ2PZqWBfeOUEK4,2382
surya/input/processing.py,sha256=v92ErFBbLWkIBrxy8imDQ79cAb_hT8m2eTVpD4ZATE4,2904
surya/layout/__init__.py,sha256=7MnYJLY_Cvx32sV2MELF01CW3nkx_a-dB_AgjuQeBQY,10533
surya/layout/__pycache__/__init__.cpython-313.pyc,,
surya/layout/__pycache__/loader.cpython-313.pyc,,
surya/layout/__pycache__/schema.cpython-313.pyc,,
surya/layout/__pycache__/slicer.cpython-313.pyc,,
surya/layout/__pycache__/util.cpython-313.pyc,,
surya/layout/loader.py,sha256=aA169iX-fJJYWqlt7jjVYO50rPKEhmySjqmRnlBKZzI,2487
surya/layout/model/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
surya/layout/model/__pycache__/__init__.cpython-313.pyc,,
surya/layout/model/__pycache__/config.cpython-313.pyc,,
surya/layout/model/__pycache__/decoder.cpython-313.pyc,,
surya/layout/model/__pycache__/encoder.cpython-313.pyc,,
surya/layout/model/__pycache__/encoderdecoder.cpython-313.pyc,,
surya/layout/model/config.py,sha256=PO9etWjOSFUELe61CtD_oUL0RsVT4gtV-4wXhfx5c1E,8234
surya/layout/model/decoder.py,sha256=YRlJn3VARq93FEzuP3GH8oG1jZ-HJ6MIuwGl8DKmhw4,5634
surya/layout/model/encoder.py,sha256=XVLVNNFpAe6mbVt2A5LPbfxJ1kQKWAAbMhqMmLjGLYU,3522
surya/layout/model/encoderdecoder.py,sha256=A5UivyQUS38h04yD1iYsE7ERKh4IbRuUrDsePxchQkg,5039
surya/layout/schema.py,sha256=dECdCKWUEKwVw8o1UP0stBqnmmgRwft4eygUYVBaDL0,390
surya/layout/slicer.py,sha256=Q_GKRYzkKHrI2QWlDeSmiYsMIjKtzfr2FjwPRJ7MFcU,6051
surya/layout/util.py,sha256=nJDggyQSv_BQ-5BFCFeWvcFxdPvFvH15CtHhnyvJ6ek,1055
surya/logging.py,sha256=Kew6K02RRu-jcs2lmLdEbnf-YrPg-4cvFMzxrA2mV9A,569
surya/models.py,sha256=O5eWdV0YRGPRDsaXALBr-a0sUrKt3X8VcNgzRMSpVPM,883
surya/ocr_error/__init__.py,sha256=iRCTD9jJjvA1pRaW3T7vp61KIvU63sxaCB0LJQSore0,2375
surya/ocr_error/__pycache__/__init__.cpython-313.pyc,,
surya/ocr_error/__pycache__/loader.cpython-313.pyc,,
surya/ocr_error/__pycache__/schema.cpython-313.pyc,,
surya/ocr_error/__pycache__/tokenizer.cpython-313.pyc,,
surya/ocr_error/loader.py,sha256=8Klp5LKF3yysOTEwqd9UwVD_4f_DGQUbtJvM1hZc_js,2014
surya/ocr_error/model/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
surya/ocr_error/model/__pycache__/__init__.cpython-313.pyc,,
surya/ocr_error/model/__pycache__/config.cpython-313.pyc,,
surya/ocr_error/model/__pycache__/encoder.cpython-313.pyc,,
surya/ocr_error/model/config.py,sha256=8HrrGfzkXNFh2pKYqBGtUyEJHUnUdXW1WQ1AJ-WIIm8,1983
surya/ocr_error/model/encoder.py,sha256=36FOq6ELuEUVow0g0Im5phvb3sUI_GxduRi00re6UKs,35371
surya/ocr_error/schema.py,sha256=or4ydCNbpUTTj-KN4AYaizXO7jfiCupnUX25EZeGHvo,143
surya/ocr_error/tokenizer.py,sha256=h9zAp0OA7g-p_cqn6p1-LLZ6-RvRUMtaHSNKL7_spo8,21391
surya/recognition/__init__.py,sha256=sWNaBn4JdkPn3hmKc3PdbGiGTUFDdDyijODQSv-tQrQ,35466
surya/recognition/__pycache__/__init__.cpython-313.pyc,,
surya/recognition/__pycache__/cache.cpython-313.pyc,,
surya/recognition/__pycache__/languages.cpython-313.pyc,,
surya/recognition/__pycache__/loader.cpython-313.pyc,,
surya/recognition/__pycache__/postprocessing.cpython-313.pyc,,
surya/recognition/__pycache__/schema.cpython-313.pyc,,
surya/recognition/__pycache__/util.cpython-313.pyc,,
surya/recognition/cache.py,sha256=6zMp9MyUNRZ5GxYS3xUVqvQHkWq_VJU7oj6IXhCZlqs,7527
surya/recognition/languages.py,sha256=G6WH3yJrPQMnm8yMy9kzn4daX2x-8OsBuBslYecVQCQ,2076
surya/recognition/loader.py,sha256=Ssdo5MwgPAH4XOXAaJWIrhcs0FlIS1fEnDdpQsYjl4o,3330
surya/recognition/postprocessing.py,sha256=TEjnGXRkGkCKl_-o4NID-pnuFjrNVeaaOnNyvDBGY5w,3261
surya/recognition/schema.py,sha256=Ls-I-OThUo8LFO15R4Ij1qMh9bHuDV_lIBrhomxFfcU,903
surya/recognition/util.py,sha256=XIfgCyB2NJ37h0dh9msAs7Z7eRT7ZFx2GNRl16QIzRc,5738
surya/scripts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
surya/scripts/__pycache__/__init__.cpython-313.pyc,,
surya/scripts/__pycache__/config.cpython-313.pyc,,
surya/scripts/__pycache__/detect_layout.cpython-313.pyc,,
surya/scripts/__pycache__/detect_text.cpython-313.pyc,,
surya/scripts/__pycache__/hf_to_s3.cpython-313.pyc,,
surya/scripts/__pycache__/ocr_latex.cpython-313.pyc,,
surya/scripts/__pycache__/ocr_text.cpython-313.pyc,,
surya/scripts/__pycache__/run_streamlit_app.cpython-313.pyc,,
surya/scripts/__pycache__/run_texify_app.cpython-313.pyc,,
surya/scripts/__pycache__/streamlit_app.cpython-313.pyc,,
surya/scripts/__pycache__/table_recognition.cpython-313.pyc,,
surya/scripts/__pycache__/texify_app.cpython-313.pyc,,
surya/scripts/config.py,sha256=WxiVayZ8_RW3JE7Jc5G-tLRU4wBmKa4VHjRc0rAKLaA,2778
surya/scripts/detect_layout.py,sha256=K5D67GKaQt0eAxH5iAMie-D3ndQydUpYVtZscFeMKxY,1827
surya/scripts/detect_text.py,sha256=YuxziJ6u1ywd8pRoKwMDjDs286DszwvfBfrV8wvJ3N4,1875
surya/scripts/hf_to_s3.py,sha256=tQ7fYR7Pc1vPVSZz9xGzbsA-aVQsox7cSDYLIzBkfs4,2074
surya/scripts/ocr_latex.py,sha256=t-1cWgkbYAKq4w4lMYNqlq_MLdkbVuvDtYb03A4Acs8,1598
surya/scripts/ocr_text.py,sha256=nBi0YhR5WKZqmse5u3e3Ttrf2ySFqYyiY5jSSXyqubQ,2350
surya/scripts/run_streamlit_app.py,sha256=s2dIGiJ1VS9Ia6PfIGezxabHYKJloLupVt1_avpcpCc,350
surya/scripts/run_texify_app.py,sha256=PbJxf2fXgVXzRLiIE8lxifHstp_6jq5XRqezOLTxgyA,344
surya/scripts/streamlit_app.py,sha256=i77VusK9Zcfm3jE2lQXg_m4OxDF3BIQClXFldDQWl_I,10363
surya/scripts/table_recognition.py,sha256=v_l2SiqNaQi1As143mtv2A2cy_ILgXULVXDChU5oSV0,4318
surya/scripts/texify_app.py,sha256=5WcL1Sj2uiDCkO0NbKymz5hPnmY_XJHPfB6wia9mtD0,4635
surya/settings.py,sha256=IeXUttPBTATz-5Dy7YKcYIw3N-qhkiQbrzjPYuT04AU,6450
surya/table_rec/__init__.py,sha256=R0TToWBxJPenoNDuvO7-K7PY4tmWKe57VHooNL7BOLY,17510
surya/table_rec/__pycache__/__init__.cpython-313.pyc,,
surya/table_rec/__pycache__/loader.cpython-313.pyc,,
surya/table_rec/__pycache__/processor.cpython-313.pyc,,
surya/table_rec/__pycache__/schema.cpython-313.pyc,,
surya/table_rec/__pycache__/shaper.cpython-313.pyc,,
surya/table_rec/loader.py,sha256=Jvfu28tgrPe7rwKtqYxodPyZ1naEXNnNLtYhCJZcV3Y,2692
surya/table_rec/model/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
surya/table_rec/model/__pycache__/__init__.cpython-313.pyc,,
surya/table_rec/model/__pycache__/config.cpython-313.pyc,,
surya/table_rec/model/__pycache__/decoder.cpython-313.pyc,,
surya/table_rec/model/__pycache__/encoder.cpython-313.pyc,,
surya/table_rec/model/__pycache__/encoderdecoder.cpython-313.pyc,,
surya/table_rec/model/config.py,sha256=7hJVhWVqX72-vHEGi6C1NgoDb8Wjq0boWUpbEB-_ij0,8035
surya/table_rec/model/decoder.py,sha256=nJN3pMMyHUtnE_H7Iew8TsNOZWV5pjOd7Fr1miXuT58,6726
surya/table_rec/model/encoder.py,sha256=lqO969wmgwPyMDhmkavBeSDdwEfKa5EnqUunmd9GCpU,3664
surya/table_rec/model/encoderdecoder.py,sha256=lTx0TAvgCN2IUVf7fkat5LFUF2JDPX_4zJKhhoKqwQg,3926
surya/table_rec/processor.py,sha256=1JRuOzq4RPq0f7X1-YjOz-0ceiO4ihgPEdRwyuEDTAc,3273
surya/table_rec/schema.py,sha256=HVNUyaJMpFlN5yOFKI4UKXeK9nueGBTjiiDJA886lag,939
surya/table_rec/shaper.py,sha256=CqanwJd7M180Zb54jpdjeniENdA3aBDkbc_kDfTSaC0,5021
surya_ocr-0.14.6.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
surya_ocr-0.14.6.dist-info/LICENSE,sha256=DZvPIbBWZPafEm8OfQkcEgcvSZE21cYB6oeDoD6wt48,35085
surya_ocr-0.14.6.dist-info/METADATA,sha256=IUzGUVY3xiHgYdpJ-zoz_Z7X6ThIbFaXq1x4vQCodXk,32661
surya_ocr-0.14.6.dist-info/RECORD,,
surya_ocr-0.14.6.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
surya_ocr-0.14.6.dist-info/WHEEL,sha256=b4K_helf-jlQoXBBETfwnf4B04YC67LOev0jo4fX5m8,88
surya_ocr-0.14.6.dist-info/entry_points.txt,sha256=-aYLRvg8I6qYH7AvKJP_fB89jXY9hsTWWy7ZudwZRBo,414
