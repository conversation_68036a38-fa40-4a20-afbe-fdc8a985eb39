#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PDF处理功能
"""

import os
import sys

def test_pdf_processing():
    """测试PDF处理功能"""
    print("开始测试PDF处理功能...")
    
    # 测试PyMuPDF
    try:
        import fitz
        print("✅ PyMuPDF (fitz) 可用")
        
        # 查找PDF文件
        pdf_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(root, file))
        
        if pdf_files:
            test_pdf = pdf_files[0]
            print(f"测试PDF文件: {test_pdf}")
            
            # 尝试打开PDF
            doc = fitz.open(test_pdf)
            print(f"PDF页数: {len(doc)}")
            
            # 转换第一页为图片
            page = doc[0]
            mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
            pix = page.get_pixmap(matrix=mat)
            
            test_image = "test_pdf_conversion.png"
            pix.save(test_image)
            doc.close()
            
            print(f"✅ PDF转换成功: {test_image}")
            
            # 检查图片文件
            if os.path.exists(test_image):
                file_size = os.path.getsize(test_image)
                print(f"图片大小: {file_size} 字节")
                
                # 清理测试文件
                try:
                    os.remove(test_image)
                    print("测试文件已清理")
                except:
                    pass
            
            return True
        else:
            print("❌ 未找到PDF文件进行测试")
            return False
            
    except ImportError:
        print("❌ PyMuPDF 未安装")
        return False
    except Exception as e:
        print(f"❌ PDF处理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_easyocr_with_pdf():
    """测试EasyOCR与PDF的完整流程"""
    print("\n开始测试完整的PDF+OCR流程...")
    
    try:
        import fitz
        import easyocr
        
        # 初始化EasyOCR
        print("初始化EasyOCR...")
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        print("EasyOCR初始化完成")
        
        # 查找PDF文件
        pdf_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(root, file))
        
        if pdf_files:
            test_pdf = pdf_files[0]
            print(f"测试PDF文件: {test_pdf}")
            
            # PDF转图片
            doc = fitz.open(test_pdf)
            page = doc[0]
            mat = fitz.Matrix(2.0, 2.0)
            pix = page.get_pixmap(matrix=mat)
            
            test_image = "test_ocr_image.png"
            pix.save(test_image)
            doc.close()
            
            print("PDF转换完成，开始OCR识别...")
            
            # OCR识别
            result = reader.readtext(test_image)
            
            print(f"✅ OCR识别完成，识别到 {len(result)} 个文本区域")
            
            # 显示前5个识别结果
            for i, (bbox, text, confidence) in enumerate(result[:5]):
                print(f"  {i+1}. '{text}' (置信度: {confidence:.2f})")
            
            # 清理测试文件
            try:
                os.remove(test_image)
                print("测试文件已清理")
            except:
                pass
            
            return True
        else:
            print("❌ 未找到PDF文件进行测试")
            return False
            
    except Exception as e:
        print(f"❌ 完整流程测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("PDF处理功能测试")
    print("=" * 50)
    
    # 测试PDF处理
    pdf_ok = test_pdf_processing()
    
    # 测试完整流程
    if pdf_ok:
        ocr_ok = test_easyocr_with_pdf()
        
        if ocr_ok:
            print("\n🎉 所有测试通过！PDF处理功能正常")
        else:
            print("\n❌ OCR测试失败")
    else:
        print("\n❌ PDF处理测试失败")
    
    input("\n按回车键退出...")
