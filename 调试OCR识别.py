#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试OCR识别结果，查看原始文本
"""

import os
import easyocr
import fitz

def debug_ocr_result(pdf_path):
    """调试OCR识别结果"""
    print(f"调试PDF文件: {pdf_path}")
    
    try:
        # 初始化EasyOCR
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        
        # PDF转图片
        doc = fitz.open(pdf_path)
        page = doc[0]
        mat = fitz.Matrix(2.0, 2.0)
        pix = page.get_pixmap(matrix=mat)
        
        temp_image = "debug_image.png"
        pix.save(temp_image)
        doc.close()
        
        print("开始OCR识别...")
        result = reader.readtext(temp_image)
        
        print(f"\n识别到 {len(result)} 个文本区域:")
        print("=" * 80)
        
        for i, (bbox, text, confidence) in enumerate(result):
            print(f"{i+1:2d}. 置信度:{confidence:.2f} | 文本: '{text}'")
        
        print("\n" + "=" * 80)
        
        # 查找关键信息
        print("\n关键信息搜索:")
        keywords = ['金额', '价税合计', '税率', '税额', '发票', '增值税', '专用', '普通', '￥', '%']
        
        for keyword in keywords:
            matches = []
            for i, (bbox, text, confidence) in enumerate(result):
                if keyword in text:
                    matches.append(f"第{i+1}行: '{text}' (置信度:{confidence:.2f})")
            
            if matches:
                print(f"\n包含'{keyword}'的文本:")
                for match in matches:
                    print(f"  {match}")
        
        # 清理临时文件
        try:
            os.remove(temp_image)
        except:
            pass
            
    except Exception as e:
        print(f"调试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 查找PDF文件
    pdf_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))
    
    if pdf_files:
        # 使用第一个PDF文件进行调试
        test_pdf = pdf_files[0]
        debug_ocr_result(test_pdf)
    else:
        print("未找到PDF文件")
    
    input("\n按回车键退出...")
