#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发票处理程序 - 使用Surya高精度OCR
基于GitHub上17.7k星标的Surya OCR项目，支持90+语言，高精度识别
"""

import win32com.client
from datetime import datetime
import os
import warnings
import tkinter as tk
from tkinter import filedialog, ttk
from tkinter import messagebox
import re
from PIL import Image
import fitz  # PyMuPDF

# Surya OCR imports
from surya.recognition import RecognitionPredictor
from surya.detection import DetectionPredictor

# 忽略警告
warnings.filterwarnings("ignore")

class SuryaOCRProcessor:
    """Surya OCR处理器 - 高精度OCR识别"""

    def __init__(self):
        """初始化Surya OCR模型"""
        print("正在初始化Surya OCR模型...")
        print("Surya OCR: 支持90+语言的高精度文档OCR系统")

        try:
            # 初始化检测和识别预测器
            print("  加载文本检测模型...")
            self.detection_predictor = DetectionPredictor()

            print("  加载文本识别模型...")
            self.recognition_predictor = RecognitionPredictor()

            print("✅ Surya OCR初始化完成！")

        except Exception as e:
            print(f"❌ Surya OCR初始化失败: {str(e)}")
            raise e

    def recognize_image(self, image_path):
        """使用Surya OCR识别图像中的文本"""
        try:
            print(f"使用Surya OCR识别图像: {image_path}")

            # 加载图像
            image = Image.open(image_path)

            # 运行OCR识别
            predictions = self.recognition_predictor([image], det_predictor=self.detection_predictor)

            # 转换为标准格式 (bbox, text, confidence)
            result = []
            if predictions and len(predictions) > 0:
                page_prediction = predictions[0]
                for text_line in page_prediction.text_lines:
                    # 获取边界框坐标
                    bbox = text_line.bbox
                    text = text_line.text
                    confidence = text_line.confidence

                    # 转换为标准格式: (bbox, text, confidence)
                    result.append((bbox, text, confidence))

            print(f"Surya OCR识别完成，共识别到 {len(result)} 个文本区域")
            return result

        except Exception as e:
            print(f"Surya OCR识别失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return []

def pdf_to_image_pymupdf(pdf_path):
    """使用PyMuPDF将PDF转换为高质量图片"""
    try:
        import fitz
        doc = fitz.open(pdf_path)
        page = doc[0]  # 获取第一页
        
        # 设置高分辨率以提高OCR识别率
        mat = fitz.Matrix(3.0, 3.0)  # 3倍缩放
        pix = page.get_pixmap(matrix=mat)
        
        temp_image_path = "temp_invoice_surya.png"
        pix.save(temp_image_path)
        doc.close()
        
        print(f"PDF转换完成: {temp_image_path}")
        return temp_image_path
    except Exception as e:
        print(f"PDF转换失败: {str(e)}")
        return None

def extract_invoice_info_surya(ocr_result):
    """从Surya OCR结果中提取发票信息"""
    result = {
        'InvoiceTypeOrg': '',
        'CommodityTaxRate': '',
        'CommodityAmount': '',
        'TotalTax': '',
        'TotalAmount': '',
        'SellerName': '',
        'Remarks': ''
    }
    
    # 将OCR结果转换为文本列表，便于处理
    all_text = [(text, confidence) for _, text, confidence in ocr_result]
    
    print(f"\n开始提取发票信息，共 {len(all_text)} 行文本")
    
    for i, (line, confidence) in enumerate(all_text):
        line = str(line).strip()
        if not line:
            continue
        
        print(f"{i+1:2d}. 置信度:{confidence:.3f} | 文本: '{line}'")
        
        # 发票类型识别 - 使用Surya的高精度识别
        if not result['InvoiceTypeOrg']:
            if '增值税专用发票' in line:
                result['InvoiceTypeOrg'] = '增值税专用发票'
                print(f"✅ 识别发票类型: {line}")
            elif '增值税普通发票' in line:
                result['InvoiceTypeOrg'] = '增值税普通发票'
                print(f"✅ 识别发票类型: {line}")
            elif '电子发票' in line:
                # 检查上下文确定是专用还是普通发票
                context_has_special = any('专用' in all_text[j][0] 
                                        for j in range(max(0, i-3), min(len(all_text), i+4)))
                if context_has_special:
                    result['InvoiceTypeOrg'] = '增值税专用发票'
                else:
                    result['InvoiceTypeOrg'] = '增值税普通发票'
                print(f"✅ 识别发票类型(上下文推断): {result['InvoiceTypeOrg']}")
        
        # 税率识别 - 直接从发票文本中识别
        if not result['CommodityTaxRate']:
            # 查找税率相关的行
            if '税率' in line or '征收率' in line:
                print(f"找到税率相关行: {line}")
                # 在税率行中查找百分比
                tax_rate_patterns = [
                    r'(\d+(?:\.\d+)?)%',
                    r'税率.*?(\d+(?:\.\d+)?)%',
                    r'征收率.*?(\d+(?:\.\d+)?)%'
                ]
                for pattern in tax_rate_patterns:
                    tax_rate_match = re.search(pattern, line)
                    if tax_rate_match:
                        rate_str = tax_rate_match.group(1)
                        result['CommodityTaxRate'] = [rate_str + '%']
                        print(f"✅ 识别税率: {line} -> {rate_str}%")
                        break
            
            # 处理单独的税率数字（在税率相关上下文中）
            elif line.strip() in ['13', '16', '17', '6', '3', '1', '0']:
                # 检查上下文是否有税率相关信息
                context_has_tax = any('税率' in all_text[j][0] or '征收率' in all_text[j][0]
                                    for j in range(max(0, i-5), min(len(all_text), i+6)))
                if context_has_tax:
                    result['CommodityTaxRate'] = [line.strip() + '%']
                    print(f"✅ 识别税率(上下文验证): {line} -> {line.strip()}%")
        
        # 金额识别 - 小写金额
        if not result['CommodityAmount']:
            # 匹配小写金额模式
            amount_patterns = [
                r'[￥¥]?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
                r'小写.*?[￥¥]?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
                r'金额.*?[￥¥]?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)'
            ]
            
            for pattern in amount_patterns:
                amount_match = re.search(pattern, line)
                if amount_match:
                    amount_str = amount_match.group(1).replace(',', '')
                    try:
                        amount_value = float(amount_str)
                        if 1000 <= amount_value <= 10000000:  # 合理的发票金额范围
                            result['CommodityAmount'] = amount_str
                            print(f"✅ 识别金额: {line} -> {amount_str}")
                            break
                    except ValueError:
                        continue
        
        # 税额识别
        if not result['TotalTax']:
            if '税额' in line:
                tax_match = re.search(r'[￥¥]?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)', line)
                if tax_match:
                    result['TotalTax'] = tax_match.group(1).replace(',', '')
                    print(f"✅ 识别税额: {line}")
        
        # 价税合计
        if not result['TotalAmount']:
            if '价税合计' in line or '合计' in line:
                total_match = re.search(r'[￥¥]?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)', line)
                if total_match:
                    result['TotalAmount'] = total_match.group(1).replace(',', '')
                    print(f"✅ 识别价税合计: {line}")
        
        # 销售方名称识别 - 强化OCR错误修正
        if not result['SellerName']:
            # 查找销售方名称
            if '销' in line and '名称' in line:
                # 提取名称部分
                name_match = re.search(r'名称[：:]\s*(.+)', line)
                if name_match:
                    seller_name = name_match.group(1).strip()
                    # 应用OCR错误修正
                    seller_name = correct_company_name_ocr(seller_name)
                    result['SellerName'] = seller_name
                    print(f"✅ 识别销售方: {line} -> {seller_name}")
            
            # 如果还没找到销售方，查找包含公司名称的行
            elif ('公司' in line or '有限' in line) and len(line) > 5:
                # 排除购方信息
                if '购' not in line and '电网' not in line and '供电局' not in line:
                    # 常见的销售方公司名称模式
                    if any(keyword in line for keyword in ['科技', '新能源', '实业', '贸易', '工程', '建设']):
                        corrected_name = correct_company_name_ocr(line.strip())
                        result['SellerName'] = corrected_name
                        if corrected_name != line.strip():
                            print(f"✅ 识别销售方(OCR修正): {line} -> {corrected_name}")
                        else:
                            print(f"✅ 识别销售方: {line}")
        
        # 备注信息识别
        if not result['Remarks']:
            if '银行账号' in line or '开户银行' in line:
                result['Remarks'] = line.strip()
                print(f"✅ 识别备注: {line}")
    
    print(f"\n发票信息提取完成:")
    for key, value in result.items():
        print(f"  {key}: {value}")
    
    return result

def correct_company_name_ocr(text):
    """OCR错误修正 - 针对公司名称的常见错误"""
    corrections = {
        # 公司名称修正
        '奥捷': ['樊捷', '奧捷', '澳捷', '敖捷', '嗷捷'],
        '奥源': ['樊源', '奧源', '澳源', '敖源', '嗷源'],
        '来宁': ['来亍', '來宁', '来寧', '來寧', '来宇'],
        '锦阳': ['锦阴', '錦阳', '锦陽', '錦陽', '锦阴'],
        '顺洋': ['順洋', '顺羊', '順羊', '顺样'],
        '揭阳': ['揭阴', '揭陽', '揭陰', '揭样'],
        '广东': ['广氽', '廣东', '廣氽', '广束'],
        '天美': ['夭美', '天羙', '夭羙', '天关'],
        
        # 其他常见错误
        '新能源': ['新龙源', '新能原', '新龙原'],
        '科技': ['科拔', '科技', '科枝'],
        '有限公司': ['有限公司', '有限公可', '有限公司'],
    }
    
    corrected_text = text
    for correct, errors in corrections.items():
        for error in errors:
            if error in corrected_text:
                corrected_text = corrected_text.replace(error, correct)
                print(f"    OCR修正: '{error}' -> '{correct}'")
    
    return corrected_text

def process_invoice_with_surya(pdf_path, keywords):
    """使用Surya OCR处理单个发票"""
    try:
        print(f"\n{'='*60}")
        print(f"处理发票: {os.path.basename(pdf_path)}")
        print(f"{'='*60}")

        # 初始化Surya OCR处理器
        if not hasattr(process_invoice_with_surya, 'ocr_processor'):
            process_invoice_with_surya.ocr_processor = SuryaOCRProcessor()

        # PDF转图片
        image_path = pdf_to_image_pymupdf(pdf_path)
        if not image_path:
            print("❌ PDF转换失败")
            return None

        # OCR识别
        ocr_result = process_invoice_with_surya.ocr_processor.recognize_image(image_path)

        # 清理临时图片
        try:
            os.remove(image_path)
        except:
            pass

        if not ocr_result:
            print("❌ OCR识别失败")
            return None

        # 提取发票信息
        invoice_info = extract_invoice_info_surya(ocr_result)

        # 检查关键词匹配
        seller_name = invoice_info.get('SellerName', '')
        matched_keyword = None

        for keyword in keywords:
            if keyword in seller_name:
                matched_keyword = keyword
                print(f"✅ 找到匹配关键词: '{keyword}' 在 '{seller_name}' 中")
                break

        if not matched_keyword:
            print(f"❌ 未找到匹配的关键词")
            print(f"   销售方名称: '{seller_name}'")
            print(f"   目标关键词: {keywords}")
            return None

        # 返回处理结果
        result = {
            'file_path': pdf_path,
            'matched_keyword': matched_keyword,
            'invoice_info': invoice_info
        }

        print(f"✅ 发票处理完成，匹配关键词: {matched_keyword}")
        return result

    except Exception as e:
        print(f"❌ 处理发票失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def create_excel_report(processed_invoices, output_path):
    """创建Excel报销表"""
    try:
        print(f"\n创建Excel报销表: {output_path}")

        # 创建Excel应用
        excel_app = win32com.client.Dispatch("Excel.Application")
        excel_app.Visible = False
        excel_app.DisplayAlerts = False

        # 创建工作簿
        workbook = excel_app.Workbooks.Add()
        worksheet = workbook.ActiveSheet
        worksheet.Name = "发票报销表"

        # 设置表头
        headers = [
            "序号", "发票类型", "销售方名称", "商品税率", "金额",
            "税额", "价税合计", "备注", "文件名", "匹配关键词"
        ]

        for col, header in enumerate(headers, 1):
            worksheet.Cells(1, col).Value = header
            worksheet.Cells(1, col).Font.Bold = True

        # 填充数据
        for row, invoice_data in enumerate(processed_invoices, 2):
            info = invoice_data['invoice_info']

            worksheet.Cells(row, 1).Value = row - 1  # 序号
            worksheet.Cells(row, 2).Value = info.get('InvoiceTypeOrg', '')
            worksheet.Cells(row, 3).Value = info.get('SellerName', '')
            worksheet.Cells(row, 4).Value = str(info.get('CommodityTaxRate', '')).replace("['", "").replace("']", "")
            worksheet.Cells(row, 5).Value = info.get('CommodityAmount', '')
            worksheet.Cells(row, 6).Value = info.get('TotalTax', '')
            worksheet.Cells(row, 7).Value = info.get('TotalAmount', '')
            worksheet.Cells(row, 8).Value = info.get('Remarks', '')
            worksheet.Cells(row, 9).Value = os.path.basename(invoice_data['file_path'])
            worksheet.Cells(row, 10).Value = invoice_data['matched_keyword']

        # 自动调整列宽
        worksheet.Columns.AutoFit()

        # 保存文件
        workbook.SaveAs(output_path)
        workbook.Close()
        excel_app.Quit()

        print(f"✅ Excel报销表创建完成: {output_path}")
        return True

    except Exception as e:
        print(f"❌ 创建Excel报销表失败: {str(e)}")
        try:
            workbook.Close()
            excel_app.Quit()
        except:
            pass
        return False

class InvoiceProcessorGUI:
    """发票处理GUI界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("发票处理程序 - Surya高精度OCR版本")
        self.root.geometry("800x600")

        # 配置变量
        self.folder_path = tk.StringVar()
        self.keywords_text = tk.StringVar(value="顺洋,来宁,锦阳,奥捷,奥源")
        self.output_path = tk.StringVar()

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_label = tk.Label(self.root, text="发票处理程序 - Surya高精度OCR",
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)

        # 说明文本
        info_text = """
基于GitHub 17.7k星标的Surya OCR项目
支持90+语言的高精度文档OCR识别
特别优化中文发票识别准确率
        """
        info_label = tk.Label(self.root, text=info_text, font=("Arial", 10), fg="blue")
        info_label.pack(pady=5)

        # 文件夹选择
        folder_frame = tk.Frame(self.root)
        folder_frame.pack(fill=tk.X, padx=20, pady=10)

        tk.Label(folder_frame, text="发票文件夹:", font=("Arial", 12)).pack(anchor=tk.W)
        folder_entry_frame = tk.Frame(folder_frame)
        folder_entry_frame.pack(fill=tk.X, pady=5)

        tk.Entry(folder_entry_frame, textvariable=self.folder_path, font=("Arial", 10)).pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Button(folder_entry_frame, text="浏览", command=self.select_folder).pack(side=tk.RIGHT, padx=(5, 0))

        # 关键词设置
        keywords_frame = tk.Frame(self.root)
        keywords_frame.pack(fill=tk.X, padx=20, pady=10)

        tk.Label(keywords_frame, text="关键词 (用逗号分隔):", font=("Arial", 12)).pack(anchor=tk.W)
        tk.Entry(keywords_frame, textvariable=self.keywords_text, font=("Arial", 10)).pack(fill=tk.X, pady=5)

        # 输出文件设置
        output_frame = tk.Frame(self.root)
        output_frame.pack(fill=tk.X, padx=20, pady=10)

        tk.Label(output_frame, text="输出Excel文件:", font=("Arial", 12)).pack(anchor=tk.W)
        output_entry_frame = tk.Frame(output_frame)
        output_entry_frame.pack(fill=tk.X, pady=5)

        tk.Entry(output_entry_frame, textvariable=self.output_path, font=("Arial", 10)).pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Button(output_entry_frame, text="浏览", command=self.select_output).pack(side=tk.RIGHT, padx=(5, 0))

        # 处理按钮
        process_button = tk.Button(self.root, text="开始处理", command=self.process_invoices,
                                 font=("Arial", 14, "bold"), bg="green", fg="white", height=2)
        process_button.pack(pady=20)

        # 进度显示
        self.progress_var = tk.StringVar(value="准备就绪")
        progress_label = tk.Label(self.root, textvariable=self.progress_var, font=("Arial", 12))
        progress_label.pack(pady=10)

        # 日志显示
        log_frame = tk.Frame(self.root)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        tk.Label(log_frame, text="处理日志:", font=("Arial", 12)).pack(anchor=tk.W)

        # 创建文本框和滚动条
        text_frame = tk.Frame(log_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(text_frame, font=("Consolas", 9))
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def select_folder(self):
        """选择发票文件夹"""
        folder = filedialog.askdirectory()
        if folder:
            self.folder_path.set(folder)

    def select_output(self):
        """选择输出文件"""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if file_path:
            self.output_path.set(file_path)

    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update()

    def process_invoices(self):
        """处理发票"""
        try:
            # 验证输入
            if not self.folder_path.get():
                messagebox.showerror("错误", "请选择发票文件夹")
                return

            if not self.keywords_text.get():
                messagebox.showerror("错误", "请输入关键词")
                return

            if not self.output_path.get():
                messagebox.showerror("错误", "请选择输出文件路径")
                return

            # 解析关键词
            keywords = [kw.strip() for kw in self.keywords_text.get().split(',') if kw.strip()]

            self.log_message("="*60)
            self.log_message("开始处理发票...")
            self.log_message(f"文件夹: {self.folder_path.get()}")
            self.log_message(f"关键词: {keywords}")
            self.log_message("="*60)

            # 查找PDF文件
            pdf_files = []
            for root, dirs, files in os.walk(self.folder_path.get()):
                for file in files:
                    if file.lower().endswith('.pdf'):
                        pdf_files.append(os.path.join(root, file))

            if not pdf_files:
                self.log_message("❌ 未找到PDF文件")
                messagebox.showwarning("警告", "在指定文件夹中未找到PDF文件")
                return

            self.log_message(f"找到 {len(pdf_files)} 个PDF文件")

            # 处理每个发票
            processed_invoices = []

            for i, pdf_path in enumerate(pdf_files, 1):
                self.progress_var.set(f"处理中... ({i}/{len(pdf_files)})")
                self.log_message(f"\n[{i}/{len(pdf_files)}] 处理: {os.path.basename(pdf_path)}")

                result = process_invoice_with_surya(pdf_path, keywords)
                if result:
                    processed_invoices.append(result)
                    self.log_message(f"✅ 成功处理，匹配关键词: {result['matched_keyword']}")
                else:
                    self.log_message(f"❌ 处理失败或未匹配")

            # 生成报告
            if processed_invoices:
                self.progress_var.set("生成Excel报告...")
                self.log_message(f"\n生成Excel报告...")

                if create_excel_report(processed_invoices, self.output_path.get()):
                    self.log_message(f"✅ 处理完成！")
                    self.log_message(f"成功处理 {len(processed_invoices)} 个发票")
                    self.log_message(f"报告保存至: {self.output_path.get()}")

                    self.progress_var.set(f"完成！成功处理 {len(processed_invoices)} 个发票")
                    messagebox.showinfo("完成", f"成功处理 {len(processed_invoices)} 个发票\n报告已保存")
                else:
                    self.log_message("❌ 生成Excel报告失败")
                    messagebox.showerror("错误", "生成Excel报告失败")
            else:
                self.log_message("❌ 没有成功处理的发票")
                self.progress_var.set("完成 - 无匹配发票")
                messagebox.showwarning("警告", "没有找到匹配关键词的发票")

        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            self.progress_var.set("处理失败")
            messagebox.showerror("错误", error_msg)

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    print("发票处理程序 - Surya高精度OCR版本")
    print("基于GitHub 17.7k星标的Surya OCR项目")
    print("支持90+语言，特别优化中文发票识别")
    print("="*60)

    # 启动GUI
    app = InvoiceProcessorGUI()
    app.run()

if __name__ == "__main__":
    main()
