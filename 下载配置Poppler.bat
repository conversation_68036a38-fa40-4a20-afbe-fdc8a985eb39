@echo off
echo 正在下载Poppler工具...
echo.

REM 创建poppler目录
if not exist "poppler" mkdir poppler
cd poppler

echo 请手动下载Poppler工具：
echo 1. 访问：https://github.com/oschwartz10612/poppler-windows/releases/
echo 2. 下载最新版本的 Release-xxx.zip 文件
echo 3. 解压到当前目录 (D:\vscode project\发票处理\poppler\)
echo 4. 确保解压后的结构为：poppler\poppler-xxx\Library\bin\
echo.
echo 或者使用以下直接下载链接：
echo https://github.com/oschwartz10612/poppler-windows/releases/download/v24.02.0-0/Release-24.02.0-0.zip
echo.
pause

echo 下载完成后，请运行程序测试PDF转换功能
pause
