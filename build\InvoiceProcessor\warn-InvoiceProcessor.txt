
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by D:\vscode project\发票处理\venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), D:\vscode project\发票处理\venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed, optional), http.server (delayed, optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), filelock._unix (conditional, optional), _pyrepl.unix_console (top-level), torch.testing._internal.distributed.distributed_test (conditional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), _pyrepl.unix_console (delayed, optional)
missing module named resource - imported by posix (top-level), fsspec.asyn (conditional, optional), torch._inductor.codecache (delayed, conditional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), tty (top-level), _pyrepl.pager (delayed, optional), _pyrepl.unix_console (top-level), _pyrepl.fancy_termios (top-level), _pyrepl.unix_eventqueue (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (delayed, conditional, optional), skimage.util.apply_parallel (delayed, conditional, optional)
missing module named multiprocessing.Pool - imported by multiprocessing (delayed, conditional), scipy._lib._util (delayed, conditional), torchvision.datasets.kinetics (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by numpy.random.bit_generator (top-level), torch.utils._backport_slots (conditional), setuptools._distutils.dist (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), pkg_resources (conditional)
missing module named jnius - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level), _pyrepl.curses (optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), rlcompleter (optional), pdb (delayed, optional), pstats (conditional, optional), sympy.interactive.session (delayed, optional), site (delayed, optional), sqlite3.__main__ (delayed, conditional, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional)
missing module named 'collections.abc' - imported by traceback (top-level), inspect (top-level), logging (top-level), typing (top-level), selectors (top-level), tracemalloc (top-level), http.client (top-level), typing_extensions (top-level), asyncio.base_events (top-level), asyncio.coroutines (top-level), requests.compat (top-level), PIL.Image (top-level), PIL._typing (top-level), numpy._typing._array_like (top-level), numpy._typing._nested_sequence (conditional), numpy._typing._shape (top-level), numpy._typing._dtype_like (top-level), numpy.lib._function_base_impl (top-level), numpy.lib._npyio_impl (top-level), yaml.constructor (top-level), numpy.random._common (top-level), numpy.random._generator (top-level), numpy.random.bit_generator (top-level), numpy.random.mtrand (top-level), numpy.polynomial._polybase (top-level), xml.etree.ElementTree (top-level), PIL.TiffImagePlugin (top-level), PIL.ImageOps (top-level), PIL.ImagePalette (top-level), PIL.ImageFilter (top-level), PIL.PngImagePlugin (top-level), torch.types (top-level), torch.autograd (top-level), torch.nn.modules.module (top-level), torch._prims_common (top-level), sympy.core.basic (top-level), sympy.core.containers (top-level), sympy.core.expr (top-level), sympy.core.function (top-level), sympy.logic.boolalg (top-level), sympy.printing.conventions (top-level), sympy.sets.sets (top-level), sympy.matrices.matrixbase (top-level), sympy.combinatorics.permutations (top-level), sympy.ntheory.factor_ (top-level), sympy.tensor.indexed (top-level), sympy.tensor.array.ndim_array (top-level), sympy.tensor.array.arrayop (top-level), sympy.tensor.array.expressions.array_expressions (top-level), sympy.polys.puiseux (conditional), sympy.matrices.sparse (top-level), sympy.strategies.core (top-level), sympy.tensor.array.expressions.from_array_to_indexed (top-level), sympy.tensor.functions (top-level), sympy.vector.coordsysrect (top-level), sympy.solvers.polysys (top-level), sympy.physics.units.util (top-level), scipy._lib._docscrape (top-level), scipy.stats._stats_py (top-level), scipy.spatial.distance (top-level), scipy._lib.doccer (top-level), scipy.integrate._quadrature (top-level), scipy.stats._continuous_distns (top-level), scipy.stats._qmc (top-level), scipy.stats._resampling (top-level), scipy.stats._multicomp (top-level), scipy.stats._sensitivity_analysis (top-level), scipy.ndimage._filters (top-level), scipy.ndimage._ni_support (top-level), scipy.linalg._decomp_cossin (top-level), sympy.plotting.series (top-level), sympy.plotting.backends.matplotlibbackend.matplotlib (top-level), sympy.integrals.manualintegrate (top-level), sympy.printing.tensorflow (top-level), torch.fx.immutable_collections (top-level), torch.utils._pytree (top-level), torch.utils._cxx_pytree (top-level), torch.export (top-level), torch.fx.graph (top-level), torch.fx.node (top-level), torch.distributed.distributed_c10d (top-level), torch._strobelight.cli_function_profiler (top-level), torch.utils._backport_slots (conditional), torch.utils.weak (top-level), torch._inductor.utils (top-level), torch._dynamo.device_interface (top-level), torch.utils._ordered_set (top-level), torch.fx.experimental.symbolic_shapes (top-level), torch._logging.structured (top-level), torch.overrides (top-level), torch.package.package_exporter (top-level), torch._decomp (top-level), torch._dispatch.python (top-level), torch.utils._python_dispatch (top-level), torchgen.code_template (conditional), torchgen.utils (conditional), torchgen.model (conditional), torchgen.local (conditional), torchgen.api.types.signatures (conditional), torchgen.api.dispatcher (conditional), torchgen.api.native (conditional), torchgen.api.translate (conditional), torchgen.api.cpp (conditional), torch.nn.attention (top-level), torch.fx.experimental.proxy_tensor (top-level), torch._library.utils (top-level), torch.utils._sympy.functions (conditional), torch._meta_registrations (top-level), torch._prims (top-level), torch.library (top-level), torch._library.custom_ops (top-level), torch._functorch.utils (top-level), torch._library.triton (top-level), torch._dynamo.symbolic_convert (top-level), torch._dynamo.bytecode_transformation (top-level), torch._dynamo.variables.base (top-level), torch._dynamo.current_scope_id (top-level), torch._dynamo.polyfills (top-level), torch._numpy._ndarray (top-level), torch._numpy._funcs_impl (conditional), torch._numpy.linalg (conditional), torch.random (top-level), torch.autograd.profiler (top-level), sqlite3.dbapi2 (top-level), torch.optim.lr_scheduler (top-level), torch.optim.optimizer (top-level), torch.optim.swa_utils (top-level), torch.profiler.profiler (top-level), torch.profiler._memory_profiler (top-level), torch._inductor.scheduler (conditional), PIL.ImageDraw (top-level), torch._inductor.runtime.runtime_utils (conditional), torch.utils._appending_byte_serializer (top-level), torch._inductor.cudagraph_utils (conditional), torch._inductor.freezing_utils (top-level), torch._inductor.output_code (conditional), torch._inductor.ir (top-level), torch._export.serde.union (top-level), torch._export.serde.serialize (top-level), torch.export.exported_program (top-level), torch._functorch.make_functional (top-level), torch.nn.utils.parametrize (top-level), torch.nn.modules.container (top-level), torch.nn.utils.rnn (top-level), torch.nn.utils._named_member_accessor (top-level), torch.nn.utils.convert_parameters (top-level), torch._functorch.aot_autograd (top-level), torch.fx.proxy (top-level), torch.testing._comparison (top-level), torch.testing._creation (top-level), torch.testing._internal.logging_tensor (top-level), torch.nn.parallel.data_parallel (top-level), torch.nn.parallel.parallel_apply (top-level), torch.nn.parallel.replicate (top-level), torch.cuda.nccl (top-level), torch.jit (top-level), torch.jit._monkeytype_config (top-level), torch._export.converter (top-level), torch.ao.quantization.pt2e._numeric_debugger (top-level), torch.ao.quantization.pt2e.graph_utils (top-level), torch.fx.passes.tools_common (top-level), torch._refs (top-level), torch._prims_common.wrappers (top-level), torch._refs.fft (top-level), torch._decomp.decompositions (top-level), torch.ao.quantization.fx.match_utils (top-level), torch._inductor.inductor_prims (conditional), torch.utils._sympy.symbol (top-level), torch._inductor.dtype_propagation (top-level), torch._inductor.dependencies (top-level), torch._inductor.loop_body (conditional), torch._inductor.index_propagation (top-level), torch._inductor.sizevars (top-level), torch._inductor.codegen.simd_kernel_features (conditional), torch._inductor.codegen.simd (conditional), torch._inductor.codegen.triton (top-level), torch._inductor.codegen.cpp_utils (top-level), torch._inductor.codegen.wrapper (conditional), torch._inductor.codegen.cpp (top-level), torch._inductor.cpp_builder (top-level), torch.torch_version (top-level), setuptools (top-level), setuptools._distutils.filelist (top-level), setuptools._distutils.util (top-level), setuptools._vendor.jaraco.functools (top-level), setuptools._vendor.more_itertools.more (top-level), setuptools._vendor.more_itertools.recipes (top-level), setuptools._distutils._modified (top-level), setuptools._distutils.compat (top-level), setuptools._distutils.spawn (top-level), setuptools._distutils.compilers.C.base (top-level), setuptools._distutils.fancy_getopt (top-level), setuptools._reqs (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools._distutils.command.bdist (top-level), setuptools._distutils.core (top-level), setuptools._distutils.cmd (top-level), setuptools._distutils.dist (top-level), configparser (top-level), setuptools._distutils.extension (top-level), setuptools.config.setupcfg (top-level), setuptools.config.expand (top-level), setuptools.config.pyprojecttoml (top-level), setuptools.config._apply_pyprojecttoml (top-level), tomllib._parser (top-level), setuptools._vendor.tomli._parser (top-level), setuptools.command.egg_info (top-level), setuptools._distutils.command.build (top-level), setuptools._distutils.command.sdist (top-level), setuptools.glob (top-level), setuptools.command._requirestxt (top-level), setuptools.command.bdist_wheel (top-level), setuptools._vendor.wheel.cli.convert (top-level), setuptools._vendor.wheel.cli.tags (top-level), torch.utils.hipify.hipify_python (top-level), setuptools.command.build_ext (top-level), setuptools._distutils.command.build_ext (top-level), _pyrepl.types (top-level), _pyrepl.readline (top-level), setuptools._distutils.compilers.C.msvc (top-level), torch._inductor.codegen.memory_planning (conditional), torch._inductor.codegen.cuda.cuda_cpp_scheduling (top-level), torch._inductor.codegen.rocm.rocm_cpp_scheduling (top-level), torch._inductor.codegen.rocm.rocm_template_buffer (top-level), torch._inductor.codegen.cuda_combined_scheduling (conditional), torch._inductor.debug (top-level), torch._dynamo.repro.after_aot (top-level), torch._dynamo.testing (top-level), torch._dynamo.backends.registry (top-level), torch._dynamo.variables.functions (top-level), torch.distributed.fsdp._flat_param (top-level), torch.distributed.fsdp._common_utils (top-level), torch.distributed.algorithms._checkpoint.checkpoint_wrapper (top-level), torch.autograd.graph (top-level), torch.testing._internal.common_utils (top-level), torch.onnx._internal.exporter._dynamic_shapes (conditional), torch.onnx._internal.exporter._ir_passes (conditional), torch.onnx._internal.exporter._onnx_program (conditional), torch.onnx._internal.exporter._core (top-level), torch.export.graph_signature (top-level), torch._functorch._aot_autograd.schemas (top-level), torch._functorch._aot_autograd.subclass_utils (top-level), torch.onnx._internal.exporter._dispatching (top-level), torch.onnx._internal.exporter._schemas (top-level), torch.onnx._internal.exporter._torchlib._torchlib_registry (top-level), torch.onnx._internal.exporter._torchlib.ops.hop (conditional), torch.onnx._internal.exporter._building (top-level), torch.onnx._internal.diagnostics.infra._infra (conditional), torch.onnx._internal.diagnostics.infra.utils (conditional), torch.onnx._internal.diagnostics.infra.context (conditional), torch.onnx._internal.diagnostics._diagnostic (conditional), torch.onnx._internal.fx.type_utils (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.fx.passes.decomp (conditional), torch._functorch.functional_call (top-level), torch.onnx._internal.fx.passes.modularization (conditional), torch.onnx._internal.fx.passes.readability (conditional), torch.onnx._internal.fx.passes.type_promotion (conditional), torch.onnx._internal.onnxruntime (top-level), torch._prims.context (top-level), torch.onnx._internal.io_adapter (conditional), torch.onnx._internal._exporter_legacy (conditional), torch.onnx._internal.fx.dynamo_graph_extractor (conditional), torch.onnx._internal.fx.fx_symbolic_graph_extractor (conditional), torch.onnx._internal.fx.decomposition_skip (conditional), torch.onnx._internal.fx.fx_onnx_interpreter (conditional), torch.fx.passes.infra.partitioner (top-level), torch.onnx._internal.jit_utils (top-level), torch.onnx._internal.registration (top-level), torch.onnx.symbolic_helper (conditional), torch.onnx.symbolic_opset9 (conditional), torch.onnx.symbolic_opset10 (conditional), torch.onnx.symbolic_opset11 (conditional), torch.onnx._internal.onnx_proto_utils (conditional), torch.onnx.utils (conditional), torch.onnx.symbolic_opset17 (top-level), torch.onnx.symbolic_opset18 (top-level), torch.onnx (conditional), torch.onnx._internal.exporter._compat (top-level), torch.testing._internal.common_device_type (top-level), torch.distributed.utils (top-level), torch.nn.parallel.scatter_gather (top-level), torch.distributed.fsdp.wrap (top-level), torch.distributed.fsdp.api (top-level), torch.distributed._shard._utils (top-level), torch.distributed._shard.sharded_tensor.utils (top-level), torch.distributed.rpc (top-level), torch.distributed.nn.api.remote_module (top-level), torch.distributed._shard.sharded_tensor.api (conditional), torch.distributed.tensor._op_schema (top-level), torch.distributed.tensor._ops.utils (top-level), torch.distributed.tensor._api (top-level), torch.distributed.tensor._dispatch (top-level), torch.distributed.tensor._sharding_prop (top-level), torch.distributed.tensor._utils (top-level), torch.distributed.checkpoint._extension (top-level), fsspec.dircache (top-level), fsspec.mapping (top-level), torch.distributed.checkpoint._fsspec_filesystem (top-level), torch.distributed.checkpoint.filesystem (top-level), torch.distributed.checkpoint.metadata (top-level), torch.distributed._state_dict_utils (top-level), torch.distributed.checkpoint.utils (top-level), torch.distributed.checkpoint._traverse (top-level), torch.distributed.checkpoint.optimizer (top-level), torch.distributed.elastic.utils.store (top-level), torch.distributed.tensor._ops._math_ops (top-level), torch.distributed.tensor._ops._pointwise_ops (top-level), torch.distributed.tensor._ops._tensor_ops (top-level), torch.distributed.tensor._ops._view_ops (top-level), torch.distributed._composable.checkpoint_activation (top-level), torch.distributed._composable.replicate (top-level), torch.distributed.fsdp._fully_shard._fsdp_state (top-level), torch.distributed.fsdp._fully_shard._fsdp_param (top-level), torch.distributed.fsdp._fully_shard._fully_shard (conditional), torch.distributed.fsdp.fully_sharded_data_parallel (top-level), torch.distributed.fsdp._init_utils (top-level), torch.distributed.fsdp._optim_utils (top-level), torch.distributed.fsdp._debug_utils (top-level), torch.distributed.fsdp._state_dict_utils (top-level), torch.distributed.fsdp._unshard_param_utils (top-level), torch._higher_order_ops.triton_kernel_wrap (top-level), torch._dynamo.side_effects (top-level), torch._dynamo.create_parameter_op (top-level), torch._dynamo.variables.builder (top-level), torch._dynamo.mutation_guard (top-level), torch._dynamo.variables.builtin (top-level), torch._dynamo.variables.torch (top-level), torch._higher_order_ops.flex_attention (top-level), torch._functorch._aot_autograd.subclass_parametrization (top-level), torch._inductor.pattern_matcher (top-level), networkx.utils.misc (top-level), networkx.utils.backends (delayed), networkx.convert (top-level), networkx.classes.coreviews (top-level), networkx.classes.reportviews (top-level), networkx.algorithms.lowest_common_ancestors (top-level), networkx.algorithms.approximation.kcomponents (top-level), networkx.generators.interval_graph (top-level), networkx.drawing.nx_pylab (delayed), torch.utils.flop_counter (top-level), torch._inductor.lowering (top-level), torch._inductor.subgraph_lowering (top-level), torch._higher_order_ops.auto_functionalize (top-level), torch.distributed._symmetric_memory (top-level), torch._inductor.autotune_process (top-level), markupsafe (top-level), torch._inductor.codegen.cpp_wrapper_cpu (top-level), torch._inductor.kernel.mm_common (top-level), torch._inductor.codegen.cpp_template_kernel (top-level), torch._inductor.codegen.cpp_template (top-level), torch._inductor.codegen.rocm.rocm_template (top-level), torch._inductor.codegen.rocm.rocm_benchmark_request (conditional), torch._inductor.codegen.rocm.rocm_kernel (top-level), torch._inductor.mkldnn_ir (top-level), torch._inductor.fx_passes.ddp_fusion (top-level), torch._inductor.fx_passes.group_batch_fusion (top-level), torch._inductor.fx_passes.pre_grad (top-level), torch.fx.experimental.optimization (top-level), torch._inductor.fx_passes.split_cat (top-level), torch._inductor.fx_passes.pad_mm (top-level), torch._inductor.compile_fx (conditional), torch._export.utils (top-level), torch._export.verifier (top-level), torch.export._unlift (top-level), torch._inductor.compile_fx_ext (conditional), torch._inductor.compile_fx_subproc (conditional), torch._inductor.cudagraph_trees (conditional), torch._dynamo.graph_deduplication (top-level), torch._inductor.codegen.common (conditional), torch._inductor.codegen.halide (conditional), torch.ao.nn.quantized.modules.linear (top-level), torch.distributed.optim.apply_optimizer_in_backward (top-level), torch.distributed.optim.named_optimizer (top-level), torch.distributed.algorithms.model_averaging.averagers (top-level), torch.distributed.algorithms.model_averaging.utils (top-level), torch._functorch._aot_autograd.runtime_wrappers (conditional), torch._functorch._aot_autograd.jit_compile_runtime_wrappers (conditional), torch._export.pass_infra.proxy_value (top-level), torch._inductor.graph (conditional), torch._inductor.runtime.triton_heuristics (conditional), torch._inductor.codecache (conditional), torch.cuda.random (top-level), torch.amp.grad_scaler (conditional), torch.utils._cpp_embed_headers (top-level), torch.xpu.random (top-level), torch._dynamo.polyfills.builtins (conditional), torch._dynamo.polyfills.functools (top-level), torch._dynamo.polyfills.itertools (conditional), torch._dynamo.polyfills.pytree (conditional), torch.testing._internal.optests.generate_tests (top-level), torch._subclasses.fake_tensor (conditional), torch.package.glob_group (top-level), torch.package.analyze.trace_dependencies (top-level), torch.package.package_importer (top-level), torchvision.transforms.transforms (top-level), torchvision.transforms.v2._utils (top-level), torchvision.transforms.v2._color (top-level), torch.utils.data.dataloader (top-level), torch.utils.data.datapipes._typing (top-level), torch.utils.data.datapipes.datapipe (top-level), torch.utils.data.datapipes.utils.common (top-level), torch.utils.data.dataset (top-level), torch.utils.data.datapipes.dataframe.structures (top-level), torch.utils.data.datapipes.iter.callable (top-level), torch.utils.data.datapipes.iter.combinatorics (top-level), torch.utils.data.sampler (top-level), torch.utils.data.datapipes.iter.combining (top-level), torch.utils.data.datapipes.iter.filelister (top-level), torch.utils.data.datapipes.iter.fileopener (top-level), torch.utils.data.datapipes.iter.grouping (top-level), torch.utils.data.datapipes.iter.sharding (top-level), torch.utils.data.datapipes.iter.routeddecoder (top-level), torch.utils.data.datapipes.iter.selecting (top-level), torch.utils.data.datapipes.iter.streamreader (top-level), torch.utils.data.datapipes.map.combinatorics (top-level), torch.utils.data.datapipes.map.combining (top-level), torch.utils.data.datapipes.map.grouping (top-level), torch.utils.data.graph (top-level), torch.utils.data.distributed (top-level), torchvision.datasets.lsun (top-level), torchvision.tv_tensors._dataset_wrapper (top-level), torch._dynamo.metrics_context (top-level), torch._dynamo.utils (conditional), torch._dynamo.callback (top-level), torch.distributed.rendezvous (top-level), torch.fx.passes.splitter_base (top-level), torch.fx.interpreter (conditional), torch.nn.modules.adaptive (top-level), torch.nn.modules.padding (top-level), torch.autograd.gradcheck (top-level), torch.functional (top-level), torch.signal.windows.windows (top-level), torch.cuda._sanitizer (top-level), skimage.io.collection (top-level), tifffile.tifffile (top-level), tifffile.zarr (conditional), shapely.decorators (top-level), shapely.strtree (top-level), pymupdf.table (top-level), pkg_resources (top-level), setuptools._vendor.platformdirs.windows (conditional), torch._export.tools (top-level), torch._inductor.fuzzer (top-level), torch.ao.quantization.quantizer.x86_inductor_quantizer (top-level), torch.distributed._shard.sharded_optim (top-level), torch.distributed._shard.sharded_optim.api (top-level), torch.distributed._tools.memory_tracker (top-level), torch.distributed.algorithms.model_averaging.hierarchical_model_averager (top-level), torch.distributed.checkpoint.state_dict (top-level), torch.distributed.elastic.control_plane (top-level), torch.distributed.elastic.utils.data.cycling_iterator (top-level), torch.distributed.fsdp.sharded_grad_scaler (top-level), torch.distributed.pipelining._backward (top-level), torch.distributed.tensor.debug._visualize_sharding (top-level), torch.distributed.tensor.experimental (top-level), torch.distributed.tensor.experimental._attention (top-level), torch.distributed.tensor.experimental._func_map (top-level), torch.distributed.tensor.experimental._register_sharding (top-level), torch.distributed.tensor.experimental._tp_transform (top-level), torch.fx.experimental.debug (top-level), torch.fx.experimental.unification.core (top-level), torch.fx.experimental.unification.unification_tools (top-level), torch.fx.experimental.migrate_gradual_types.constraint_generator (top-level), torch.nn.utils.prune (top-level), torch.onnx._experimental (top-level), torch.onnx.verification (top-level), torch.utils.benchmark.utils.common (top-level), torch.utils.benchmark.utils.valgrind_wrapper.timer_interface (top-level), torch.testing._internal.opinfo.core (top-level), torch.testing._internal.opinfo.utils (top-level), torch.testing._internal.opinfo.definitions._masked (top-level), torch.testing._internal.common_methods_invocations (top-level), torch.testing._internal.opinfo.definitions.linalg (top-level), scipy.signal._short_time_fft (top-level), torch.testing._internal.common_nn (top-level), torch.testing._internal.distributed._tensor.common_dtensor (top-level), torch.testing._internal.hypothesis_utils (top-level), torch.utils._strobelight.cli_function_profiler (top-level), torch.utils.benchmark.examples.spectral_ops_fuzz_test (top-level), torch.utils.bundled_inputs (top-level), PIL.Jpeg2KImagePlugin (top-level), PIL.IptcImagePlugin (top-level), scipy._lib.array_api_compat.common._fft (conditional), scipy.constants._codata (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named numpy.VisibleDeprecationWarning - imported by numpy (conditional), scipy._lib._util (conditional)
missing module named numpy.ComplexWarning - imported by numpy (conditional), scipy._lib._util (conditional)
missing module named numpy.AxisError - imported by numpy (conditional), scipy._lib._util (conditional), skimage.color.colorconv (optional)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional), torch._jit_internal (optional)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional), scipy._lib._testutils (delayed, optional), torch.testing._internal.common_device_type (optional), torch._numpy.testing.utils (delayed, optional)
missing module named numpy.random.RandomState - imported by numpy.random (top-level), numpy.random._generator (top-level), networkx.utils.backends (delayed)
missing module named threadpoolctl - imported by numpy.lib._utils_impl (delayed, optional), scipy.io._fast_matrix_market (optional)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._function_base_impl (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy.lib._utils_impl (top-level), numpy (conditional)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.longlong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._array_utils_impl (top-level), numpy (conditional), numpy.fft._helper (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named tabulate - imported by torch.ao.ns.fx.n_shadows_utils (delayed, optional), torch._inductor.wrapper_benchmark (delayed), torch.utils.flop_counter (delayed), torch._dynamo.backends.distributed (delayed, conditional, optional), torch._dynamo.utils (delayed, optional), torch.fx.graph (delayed, optional), torch.ao.quantization.fx._model_report.model_report_visualizer (optional), torch.distributed._tools.mem_tracker (delayed, optional), torch.distributed._tools.sac_estimator (delayed, optional), torch.distributed.tensor.debug._visualize_sharding (delayed, optional), torch.distributed.tensor.debug._op_coverage (delayed), torch.utils.benchmark.utils.compile (optional)
missing module named dill - imported by torch.utils._import_utils (delayed), torch.utils.data.graph (delayed, conditional, optional)
missing module named pytest - imported by scipy._lib._testutils (delayed), sympy.testing.runtests_pytest (optional), torch.testing._internal.common_utils (delayed, conditional, optional), networkx.utils.backends (delayed, conditional, optional), torch.testing._internal.optests.generate_tests (delayed, conditional), skimage._shared.tester (delayed), fsspec.conftest (top-level), torch._numpy.testing.utils (delayed)
missing module named cupy_backends - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'cupy.cuda' - imported by scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'jax.experimental' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'jax.numpy' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'dask.array' - imported by scipy._lib.array_api_compat.dask.array (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional), skimage.util.apply_parallel (delayed, optional)
missing module named sparse - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional)
missing module named dask - imported by scipy._lib.array_api_compat.common._helpers (delayed), fsspec.implementations.dask (top-level)
missing module named ndonnx - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named cupy - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.cupy (top-level), scipy._lib.array_api_compat.cupy._aliases (top-level), scipy._lib.array_api_compat.cupy._info (top-level), scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib._array_api (delayed, conditional)
missing module named jax - imported by scipy._lib.array_api_compat.common._helpers (delayed), scipy._lib._array_api (delayed, conditional)
missing module named Cython - imported by scipy._lib._testutils (optional)
missing module named cython - imported by scipy._lib._testutils (optional)
missing module named sphinx - imported by scipy._lib._docscrape (delayed, conditional)
missing module named scipy.sparse.csc_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.optimize._milp (top-level), scipy.io._harwell_boeing.hb (top-level)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named cupyx - imported by scipy._lib._array_api (delayed, conditional)
missing module named scipy.special.sph_jn - imported by scipy.special (delayed, conditional, optional), sympy.functions.special.bessel (delayed, conditional, optional)
missing module named scipy.special.gammaincinv - imported by scipy.special (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog_backend (top-level), scipy.stats._multivariate (top-level)
missing module named 'matplotlib.pyplot' - imported by scipy.stats._distribution_infrastructure (delayed, optional), scipy.stats._fit (delayed, conditional), scipy.stats._survival (delayed, conditional), torch.profiler._memory_profiler (delayed), torch.utils.tensorboard._utils (delayed), networkx.drawing.nx_pylab (delayed), torch._functorch.partitioners (delayed, conditional), skimage.io._plugins.matplotlib_plugin (delayed), torch.ao.quantization.fx._model_report.model_report_visualizer (optional), torch.distributed._tools.memory_tracker (delayed), torch.distributed._tools.sac_estimator (delayed, optional)
missing module named matplotlib - imported by scipy.spatial._plotutils (delayed), scipy.stats._fit (delayed, optional), scipy.stats._survival (delayed, optional), sympy.testing.runtests (delayed, conditional), networkx.drawing.nx_pylab (delayed), torch._functorch._activation_checkpointing.graph_info_provider (delayed), tifffile.tifffile (delayed, conditional, optional), imageio.plugins._tifffile (delayed, conditional, optional)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named 'matplotlib.ticker' - imported by scipy.stats._fit (delayed)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named cffi - imported by scipy._lib._ccallback (delayed, optional)
missing module named 'matplotlib.collections' - imported by scipy.spatial._plotutils (delayed), networkx.drawing.nx_pylab (delayed)
missing module named 'scikits.umfpack' - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named scipy.optimize.root_scalar - imported by scipy.optimize (top-level), scipy.stats._continuous_distns (top-level), scipy.stats._stats_py (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.optimize.brentq - imported by scipy.optimize (delayed), scipy.integrate._ivp.ivp (delayed), scipy.stats._binomtest (top-level), scipy.stats._odds_ratio (top-level)
missing module named scipy.optimize.OptimizeResult - imported by scipy.optimize (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.ivp (top-level), scipy._lib.cobyqa.main (top-level), scipy._lib.cobyqa.problem (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._lsq.trf_linear (top-level), scipy.optimize._lsq.bvls (top-level), scipy.optimize._spectral (top-level), scipy.optimize._differentialevolution (top-level), scipy.optimize._shgo (top-level), scipy.optimize._dual_annealing (top-level), scipy.optimize._qap (top-level), scipy.optimize._direct_py (top-level)
missing module named scipy.optimize.minimize_scalar - imported by scipy.optimize (top-level), scipy.interpolate._bsplines (top-level), scipy.stats._multicomp (top-level)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named scipy.linalg.orthogonal_procrustes - imported by scipy.linalg (top-level), scipy.spatial._procrustes (top-level)
missing module named scipy.linalg.cholesky - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._optimize (top-level), scipy.optimize._minpack_py (top-level)
missing module named scipy.linalg.cho_solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._trustregion_exact (top-level), scipy.optimize._lsq.common (top-level)
missing module named scipy.linalg.cho_factor - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._lsq.common (top-level)
missing module named scipy.linalg.inv - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._nonlin (top-level)
missing module named scipy.linalg.lu_solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.linalg.lu_factor - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.linalg.eigh - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy._lib.cobyqa.models (top-level)
missing module named scipy.linalg.eig - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scipy.linalg.lstsq - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.signal._fir_filter_design (top-level), scipy.signal._savitzky_golay (top-level)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.linalg.svd - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.sparse.linalg._eigen._svds (top-level), scipy.linalg._decomp_polar (top-level), scipy.optimize._minpack_py (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._nonlin (top-level), scipy.optimize._remove_redundancy (top-level)
missing module named scipy.linalg.solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._cubic (top-level), scipy.optimize._nonlin (top-level), scipy.optimize._linprog_rs (top-level), scipy.signal._fir_filter_design (top-level)
missing module named scipy.linalg.qr - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy._lib.cobyqa.subsolvers.optim (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.trf_linear (top-level), scipy.optimize._nonlin (top-level), scipy.signal._ltisys (top-level)
missing module named scikits - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.sparse.diags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.spdiags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.dia_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.kron - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.eye - imported by scipy.sparse (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.optimize._trustregion_constr.equality_constrained_sqp (top-level), scipy.optimize._trustregion_constr.projections (top-level)
missing module named scipy.sparse.diags_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.eye_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.csr_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._ndbspline (top-level)
missing module named scipy.sparse.SparseEfficiencyWarning - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.issparse - imported by scipy.sparse (top-level), scipy.sparse.linalg._interface (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._norm (top-level), scipy.sparse.csgraph._laplacian (top-level), scipy._lib._array_api (delayed), scipy.integrate._ivp.bdf (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.radau (top-level), scipy.optimize._constraints (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._differentialevolution (top-level), scipy.optimize._milp (top-level), scipy.io.matlab._mio (delayed, conditional), scipy.io._fast_matrix_market (top-level), scipy.io._mmio (top-level), scipy.sparse.csgraph._validation (top-level)
missing module named scipy._distributor_init_local - imported by scipy (optional), scipy._distributor_init (optional)
missing module named torchaudio - imported by torch.utils.data.datapipes.utils.decoder (delayed, optional)
missing module named av - imported by torchvision.io.video (optional), torchvision.io.video_reader (optional), imageio.plugins.pyav (top-level)
missing module named accimage - imported by torchvision.transforms.transforms (optional), torchvision.transforms.functional (optional), torchvision.transforms._functional_pil (optional), torchvision.datasets.folder (delayed)
missing module named 'av.video' - imported by torchvision.io.video (delayed, optional)
missing module named torchvision_extra_decoders - imported by torchvision.io.image (delayed, optional)
missing module named pycocotools - imported by torchvision.datasets.coco (delayed), torchvision.tv_tensors._dataset_wrapper (delayed)
missing module named gdown - imported by torchvision.datasets.utils (delayed, optional)
missing module named 'defusedxml.ElementTree' - imported by torchvision.datasets.voc (optional)
missing module named h5py - imported by torchvision.datasets.pcam (delayed, optional)
missing module named lmdb - imported by torchvision.datasets.lsun (delayed)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named PIL._avif - imported by PIL (optional), PIL.AvifImagePlugin (optional)
missing module named defusedxml - imported by PIL.Image (optional), tifffile.tifffile (delayed, optional)
missing module named torcharrow - imported by torch.utils.data.datapipes.iter.callable (delayed, conditional, optional)
missing module named pandas - imported by networkx.convert (delayed, optional), networkx.convert_matrix (delayed), networkx.algorithms.centrality.group (delayed), torch.utils.data.datapipes.dataframe.dataframe_wrapper (delayed, optional), pymupdf.table (delayed, optional), fsspec.implementations.reference (delayed)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named 'Cython.Distutils' - imported by setuptools.command.build_ext (conditional, optional)
missing module named 'conda.cli' - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named conda - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named 'triton.runtime' - imported by torch.utils._triton (delayed), torch._inductor.runtime.triton_compat (conditional, optional), torch._inductor.runtime.runtime_utils (delayed, optional), torch._higher_order_ops.triton_kernel_wrap (delayed, conditional), torch._inductor.select_algorithm (delayed, optional), torch._inductor.fx_passes.reinplace (delayed, conditional), torch._dynamo.variables.builder (delayed, conditional), torch._inductor.ir (delayed), torch._inductor.codecache (delayed, conditional), torch._library.triton (delayed), torch._inductor.utils (delayed)
missing module named 'triton.compiler' - imported by torch.utils._triton (delayed), torch._inductor.runtime.hints (conditional), torch._inductor.runtime.triton_compat (conditional, optional), torch._inductor.codegen.triton (delayed), torch._higher_order_ops.triton_kernel_wrap (delayed), torch._inductor.async_compile (delayed, optional), torch._inductor.scheduler (delayed), torch._inductor.codecache (delayed, optional), torch._inductor.utils (delayed)
missing module named 'triton.backends' - imported by torch.utils._triton (delayed), torch._inductor.runtime.hints (conditional), torch._inductor.runtime.triton_compat (conditional, optional), torch._higher_order_ops.triton_kernel_wrap (delayed, conditional), torch._inductor.utils (delayed)
missing module named 'triton.language' - imported by torch.utils._triton (delayed, conditional, optional), torch._inductor.runtime.triton_compat (conditional, optional), torch._inductor.codegen.wrapper (delayed), torch._inductor.codegen.triton_split_scan (delayed), torch.sparse._triton_ops (conditional), torch.testing._internal.triton_utils (conditional)
missing module named 'triton.tools' - imported by torch.utils._triton (delayed, conditional, optional), torch._higher_order_ops.triton_kernel_wrap (delayed, conditional), torch._dynamo.variables.builder (delayed, conditional)
missing module named triton - imported by torch.utils._triton (delayed, optional), torch._inductor.runtime.hints (conditional), torch._dynamo.logging (conditional, optional), torch._inductor.runtime.triton_compat (conditional, optional), torch._inductor.codegen.wrapper (delayed, conditional), torch._higher_order_ops.triton_kernel_wrap (delayed), torch._inductor.kernel.mm_common (delayed), torch._inductor.kernel.mm (optional), torch._inductor.kernel.mm_plus_mm (delayed), torch._inductor.ir (optional), torch._functorch._aot_autograd.autograd_cache (delayed, conditional), torch.sparse._triton_ops_meta (delayed, conditional), torch.sparse._triton_ops (conditional), torch._dynamo.utils (conditional), torch._inductor.compile_worker.__main__ (optional), torch.testing._internal.inductor_utils (conditional), torch.testing._internal.triton_utils (conditional)
missing module named 'torch._C._profiler' - imported by torch.utils._traceback (delayed), torch.cuda._memory_viz (delayed), torch.profiler (top-level), torch.autograd.profiler (top-level), torch.profiler.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.testing._internal.logging_tensor (top-level), torch.autograd (top-level), torch.profiler._pattern_matcher (top-level)
missing module named flint - imported by sympy.external.gmpy (delayed, optional), sympy.polys.polyutils (conditional), sympy.polys.factortools (conditional), sympy.polys.polyclasses (conditional), sympy.polys.domains.groundtypes (conditional), sympy.polys.domains.finitefield (conditional)
missing module named py - imported by mpmath.tests.runtests (delayed, conditional)
missing module named mpl_toolkits - imported by mpmath.visualization (delayed)
missing module named pylab - imported by mpmath.visualization (delayed, conditional)
missing module named 'sage.libs' - imported by mpmath.libmp.backend (conditional, optional), mpmath.libmp.libelefun (conditional, optional), mpmath.libmp.libmpf (conditional, optional), mpmath.libmp.libmpc (conditional, optional), mpmath.libmp.libhyper (delayed, conditional), mpmath.ctx_mp (conditional)
missing module named sage - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy2 - imported by mpmath.libmp.backend (conditional, optional), sympy.polys.domains.groundtypes (conditional), sympy.testing.runtests (delayed, conditional)
missing module named 'pyglet.image' - imported by sympy.printing.preview (delayed, optional)
missing module named 'pyglet.window' - imported by sympy.plotting.pygletplot.managed_window (top-level), sympy.plotting.pygletplot.plot_controller (top-level), sympy.printing.preview (delayed, optional)
missing module named pyglet - imported by sympy.plotting.pygletplot.plot (optional), sympy.plotting.pygletplot.plot_axes (top-level), sympy.printing.preview (delayed, conditional, optional), sympy.testing.runtests (delayed, conditional)
missing module named 'pyglet.gl' - imported by sympy.plotting.pygletplot.plot_axes (top-level), sympy.plotting.pygletplot.util (top-level), sympy.plotting.pygletplot.plot_window (top-level), sympy.plotting.pygletplot.plot_camera (top-level), sympy.plotting.pygletplot.plot_rotation (top-level), sympy.plotting.pygletplot.plot_curve (top-level), sympy.plotting.pygletplot.plot_mode_base (top-level), sympy.plotting.pygletplot.plot_surface (top-level)
missing module named 'pyglet.clock' - imported by sympy.plotting.pygletplot.managed_window (top-level)
missing module named lxml - imported by sympy.utilities.mathml (delayed), tifffile.tifffile (delayed, optional)
missing module named all - imported by sympy.testing.runtests (delayed, optional)
missing module named 'IPython.Shell' - imported by sympy.interactive.session (delayed, conditional)
missing module named 'IPython.frontend' - imported by sympy.interactive.session (delayed, conditional)
missing module named 'IPython.terminal' - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional)
missing module named 'IPython.iplib' - imported by sympy.interactive.printing (delayed, optional)
missing module named 'IPython.core' - imported by sympy.interactive.printing (delayed, optional)
missing module named IPython - imported by sympy.interactive.printing (delayed, optional), sympy.interactive.session (delayed, conditional, optional)
missing module named pysat - imported by sympy.logic.algorithms.minisat22_wrapper (delayed)
missing module named pycosat - imported by sympy.logic.algorithms.pycosat_wrapper (delayed)
missing module named 'sage.all' - imported by sympy.core.function (delayed)
missing module named 'sage.interfaces' - imported by sympy.core.basic (delayed)
missing module named optree - imported by torch.utils._cxx_pytree (top-level), torch._dynamo.polyfills.pytree (conditional)
missing module named 'hypothesis.strategies' - imported by torch.testing._internal.hypothesis_utils (top-level)
missing module named 'hypothesis.extra' - imported by torch.testing._internal.hypothesis_utils (top-level)
missing module named hypothesis - imported by torch.testing._internal.common_utils (optional), torch.testing._internal.hypothesis_utils (top-level)
missing module named pytest_subtests - imported by torch.testing._internal.opinfo.core (delayed, conditional, optional)
missing module named 'torch._C._distributed_c10d' - imported by torch.distributed (conditional), torch.distributed.device_mesh (conditional), torch.distributed.distributed_c10d (top-level), torch._inductor.codegen.wrapper (delayed, optional), torch.distributed.rpc (conditional), torch.distributed._shard.sharded_tensor.reshard (top-level), torch.distributed._shard.sharding_spec.chunk_sharding_spec_ops.embedding_bag (top-level), torch.distributed.tensor._collective_utils (top-level), torch.testing._internal.distributed.fake_pg (top-level), torch._dynamo.variables.distributed (delayed), torch.distributed._symmetric_memory (top-level), torch.distributed.constants (top-level), torch.distributed._tools.fake_collectives (top-level), torch.distributed.elastic.control_plane (delayed), torch.testing._internal.common_distributed (top-level), torch.testing._internal.distributed.multi_threaded_pg (top-level)
missing module named objgraph - imported by torch.testing._internal.common_utils (delayed, conditional, optional)
missing module named 'xmlrunner.result' - imported by torch.testing._internal.common_utils (delayed, conditional)
missing module named xmlrunner - imported by torch.testing._internal.common_utils (delayed, conditional)
missing module named torch.nn.Sequential - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ModuleList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ModuleDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named expecttest - imported by torch.testing._internal.common_utils (top-level)
missing module named torch.ao.quantization.QConfigMapping - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.custom_config (top-level), torch.ao.ns.fx.n_shadows_utils (top-level), torch.ao.ns.fx.qconfig_multi_mapping (top-level), torch.ao.ns._numeric_suite_fx (top-level), torch.ao.quantization.fx.lstm_utils (top-level), torch.ao.quantization.pt2e.prepare (top-level), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.QConfig - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.qconfig_mapping_utils (top-level), torch.ao.quantization.fx.lstm_utils (top-level), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.QuantType - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.utils (top-level), torch.testing._internal.common_quantization (top-level)
missing module named transformers - imported by torch.onnx._internal.fx.patcher (delayed, conditional, optional), torch.onnx._internal.fx.dynamo_graph_extractor (delayed, optional), torch.testing._internal.common_distributed (delayed, optional)
missing module named 'torch._C._autograd' - imported by torch._subclasses.meta_utils (top-level), torch.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.distributed._symmetric_memory (top-level), torch.autograd (top-level), torch.testing._internal.common_distributed (top-level)
missing module named numba - imported by torch.testing._internal.common_cuda (conditional, optional)
missing module named torch.tensor - imported by torch (top-level), torch.utils.benchmark.utils.compare (top-level)
missing module named torch.nn.Module - imported by torch.nn (top-level), torch.optim.swa_utils (top-level), torch.jit._recursive (top-level), torch.jit._script (top-level), torch.jit._trace (top-level), torch.ao.quantization.fake_quantize (top-level), torch.fx.passes.utils.common (top-level), torch.distributed.nn.api.remote_module (top-level), torch._dynamo.mutation_guard (top-level), torch.fx.experimental.proxy_tensor (top-level)
missing module named onnx - imported by torch.utils.tensorboard._onnx_graph (delayed), torch.onnx._internal._lazy_import (conditional), torch.onnx._internal.exporter._schemas (top-level), torch.onnx._internal.exporter._building (conditional), torch.onnx._internal.fx.serialization (delayed, conditional), torch.onnx._internal.fx.type_utils (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (delayed), torch.onnx._internal.onnxruntime (conditional), torch.onnx._internal._exporter_legacy (delayed, optional), torch.onnx._internal.onnx_proto_utils (delayed, optional), torch.onnx.verification (delayed, optional)
missing module named onnxruntime - imported by torch.onnx._internal.exporter._onnx_program (delayed, conditional), torch.onnx._internal.onnxruntime (delayed, conditional), torch.onnx._internal._exporter_legacy (conditional), torch.onnx.verification (delayed, optional)
missing module named 'torch._C._onnx' - imported by torch.onnx (top-level), torch.onnx.utils (top-level), torch.onnx.symbolic_helper (top-level), torch.onnx._globals (top-level), torch.onnx.symbolic_opset9 (top-level), torch.onnx.symbolic_opset10 (top-level), torch.onnx.symbolic_opset13 (top-level), torch.onnx._experimental (top-level), torch.onnx.verification (top-level)
missing module named 'onnxruntime.capi' - imported by torch.onnx._internal.onnxruntime (delayed, conditional)
missing module named 'onnx.defs' - imported by torch.onnx._internal.fx.type_utils (delayed, conditional)
missing module named safetensors - imported by torch.onnx._internal.fx.patcher (delayed, conditional, optional), torch.onnx._internal.fx.serialization (delayed)
missing module named onnxscript - imported by torch.onnx._internal._lazy_import (conditional), torch.onnx._internal.exporter._core (top-level), torch.onnx._internal.exporter._dispatching (top-level), torch.onnx._internal.exporter._schemas (top-level), torch.onnx._internal.exporter._torchlib._torchlib_registry (top-level), torch.onnx._internal.exporter._torchlib._tensor_typing (top-level), torch.onnx._internal.exporter._building (top-level), torch.onnx._internal.exporter._tensors (top-level), torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.registration (conditional), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.exporter._verification (conditional), torch.onnx._internal.exporter._reporting (conditional), torch.onnx._internal._exporter_legacy (delayed, conditional, optional), torch.onnx._internal.fx.fx_onnx_interpreter (top-level)
missing module named 'onnxscript.function_libs' - imported by torch.onnx._internal.exporter._ir_passes (delayed, optional), torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.fx.decomposition_skip (top-level), torch.onnx._internal.fx.fx_onnx_interpreter (top-level)
missing module named 'onnxscript.ir' - imported by torch.onnx._internal.exporter._core (top-level), torch.onnx._internal.exporter._torchlib.ops.hop (delayed), torch.onnx._internal.exporter._building (top-level)
missing module named 'onnxscript.onnx_opset' - imported by torch.onnx._internal.exporter._torchlib.ops.core (top-level)
missing module named pyinstrument - imported by torch.onnx._internal.exporter._core (delayed, conditional)
missing module named 'onnxscript.evaluator' - imported by torch.onnx._internal.exporter._core (top-level)
missing module named 'onnxscript._framework_apis' - imported by torch.onnx._internal._lazy_import (conditional)
missing module named torch.multiprocessing._prctl_pr_set_pdeathsig - imported by torch.multiprocessing (top-level), torch.multiprocessing.spawn (top-level)
missing module named 'tensorboard.summary' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard (top-level)
missing module named 'google.protobuf' - imported by torch.utils.tensorboard.writer (delayed)
missing module named moviepy - imported by torch.utils.tensorboard.summary (delayed, optional)
missing module named google - imported by torch.utils.tensorboard.summary (top-level)
missing module named 'matplotlib.backends' - imported by torch.utils.tensorboard._utils (delayed)
missing module named 'tensorboard.plugins' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard._embedding (top-level), torch.utils.tensorboard.summary (top-level)
missing module named 'tensorboard.compat' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard._embedding (top-level), torch.utils.tensorboard._onnx_graph (top-level), torch.utils.tensorboard._pytorch_graph (top-level), torch.utils.tensorboard._proto_graph (top-level), torch.utils.tensorboard.summary (top-level)
missing module named 'matplotlib.figure' - imported by torch.utils.tensorboard.writer (conditional)
missing module named tensorboard - imported by torch.utils.tensorboard (top-level)
missing module named 'torch._C._monitor' - imported by torch.monitor (top-level)
missing module named astunparse - imported by torch.jit.frontend (optional)
missing module named 'torch._C._jit_tree_views' - imported by torch._sources (top-level), torch.jit.frontend (top-level)
missing module named torch.TensorType - imported by torch (top-level), torch.jit._passes._property_propagation (top-level)
missing module named 'monkeytype.tracing' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.db' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.config' - imported by torch.jit._monkeytype_config (optional)
missing module named monkeytype - imported by torch.jit._monkeytype_config (optional)
missing module named pydot - imported by networkx.drawing.nx_pydot (delayed), torch._functorch.partitioners (delayed), torch.fx.passes.graph_drawer (optional)
missing module named z3 - imported by torch.fx.experimental.validator (optional), torch.fx.experimental.migrate_gradual_types.transform_to_z3 (optional), torch.fx.experimental.migrate_gradual_types.z3_types (optional)
missing module named 'torch_xla.distributed' - imported by torch.distributed.tensor._api (delayed, conditional, optional)
missing module named torch_xla - imported by torch.distributed.tensor._api (delayed, conditional, optional), torch._tensor (delayed, conditional)
missing module named 'torch._C._distributed_rpc' - imported by torch.distributed.rpc (conditional), torch.distributed.rpc.api (top-level), torch.distributed.rpc.constants (top-level), torch.distributed.rpc.internal (top-level), torch.distributed.rpc.options (top-level), torch._jit_internal (conditional)
missing module named 'torch._C._distributed_rpc_testing' - imported by torch.distributed.rpc._testing (conditional)
missing module named torch.distributed.ReduceOp - imported by torch.distributed (top-level), torch.distributed.nn.functional (top-level)
missing module named torch.distributed.group - imported by torch.distributed (top-level), torch.distributed.nn.functional (top-level), torch.distributed.algorithms.model_averaging.utils (top-level)
missing module named torchdistx - imported by torch.distributed.fsdp._init_utils (optional)
missing module named etcd - imported by torch.distributed.elastic.rendezvous.etcd_rendezvous (optional), torch.distributed.elastic.rendezvous.etcd_store (optional), torch.distributed.elastic.rendezvous.etcd_rendezvous_backend (optional), torch.distributed.elastic.rendezvous.etcd_server (optional)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional), fsspec.compression (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional)
missing module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level), fsspec.implementations.http_sync (delayed, optional)
missing module named 'torch.distributed.elastic.metrics.static_init' - imported by torch.distributed.elastic.metrics (optional)
missing module named 'safetensors.torch' - imported by torch.distributed.checkpoint.filesystem (delayed, conditional, optional), torch.distributed.checkpoint._hf_storage (delayed)
missing module named huggingface_hub - imported by torch.distributed.checkpoint._hf_storage (delayed)
missing module named distributed - imported by fsspec.transaction (delayed)
missing module named 'pyarrow.parquet' - imported by fsspec.parquet (delayed)
missing module named fastparquet - imported by fsspec.parquet (delayed)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named requests_kerberos - imported by fsspec.implementations.webhdfs (delayed, conditional)
missing module named smbprotocol - imported by fsspec.implementations.smb (top-level)
missing module named smbclient - imported by fsspec.implementations.smb (top-level)
missing module named paramiko - imported by fsspec.implementations.sftp (top-level)
missing module named kerchunk - imported by fsspec.implementations.reference (delayed)
missing module named ujson - imported by fsspec.implementations.cache_metadata (optional), fsspec.implementations.reference (optional)
missing module named 'libarchive.ffi' - imported by fsspec.implementations.libarchive (top-level)
missing module named libarchive - imported by fsspec.implementations.libarchive (top-level)
missing module named yarl - imported by fsspec.implementations.http (top-level), fsspec.implementations.http_sync (optional)
missing module named aiohttp - imported by fsspec.implementations.http (top-level)
missing module named pygit2 - imported by fsspec.implementations.git (top-level)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named 'distributed.worker' - imported by fsspec.implementations.dask (top-level)
missing module named 'distributed.client' - imported by fsspec.implementations.dask (top-level)
missing module named 'pyarrow.fs' - imported by fsspec.implementations.arrow (delayed)
missing module named pyarrow - imported by fsspec.implementations.arrow (delayed)
missing module named panel - imported by fsspec.gui (top-level)
missing module named fuse - imported by fsspec.fuse (top-level)
missing module named lz4 - imported by fsspec.compression (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named tqdm - imported by torch.hub (optional), fsspec.callbacks (delayed, optional)
missing module named 'torch._C._distributed_autograd' - imported by torch.distributed.autograd (conditional)
missing module named pulp - imported by torch.distributed._tools.sac_ilp (optional)
missing module named pwlf - imported by torch.distributed._tools.sac_estimator (delayed, optional)
missing module named amdsmi - imported by torch.cuda (conditional, optional), torch.cuda.memory (delayed, conditional, optional)
missing module named pynvml - imported by torch.cuda (delayed, conditional, optional), torch.cuda.memory (delayed, conditional, optional)
missing module named 'tensorflow.python' - imported by torch.contrib._tensorboard_vis (optional)
missing module named 'tensorflow.core' - imported by torch.contrib._tensorboard_vis (optional)
missing module named tensorflow - imported by torch.contrib._tensorboard_vis (optional)
missing module named opt_einsum - imported by torch.backends.opt_einsum (optional)
missing module named 'coremltools.models' - imported by torch.backends._coreml.preprocess (top-level)
missing module named 'coremltools.converters' - imported by torch.backends._coreml.preprocess (top-level)
missing module named coremltools - imported by torch.backends._coreml.preprocess (top-level)
missing module named torch.ao.quantization.QConfigAny - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.utils (top-level)
missing module named pytorch_lightning - imported by torch.ao.pruning._experimental.data_sparsifier.lightning.callbacks.data_sparsity (top-level)
missing module named torch.nn.ReLU - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Linear - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named 'torch._C._functorch' - imported by torch._functorch.pyfunctorch (top-level), torch._subclasses.meta_utils (top-level), torch._functorch.autograd_function (top-level), torch._functorch.utils (top-level), torch._functorch.vmap (top-level), torch._functorch.eager_transforms (top-level), torch._higher_order_ops.cond (top-level), torch._subclasses.fake_tensor (top-level)
missing module named torch._numpy.float_ - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.max - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isnan - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.signbit - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.real - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isscalar - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.iscomplexobj - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.imag - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.intp - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.empty - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.arange - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named fbscribelogger - imported by torch._logging.scribe (optional)
missing module named 'torch._C._lazy_ts_backend' - imported by torch._lazy.ts_backend (top-level), torch._lazy.computation (top-level)
missing module named 'torch._C._lazy' - imported by torch._lazy (top-level), torch._lazy.device_context (top-level), torch._lazy.metrics (top-level), torch._lazy.computation (top-level), torch._lazy.config (top-level), torch._lazy.debug (top-level), torch._lazy.ir_cache (top-level)
missing module named 'torch._inductor.fb' - imported by torch._inductor.remote_cache (delayed, conditional, optional), torch._dynamo.pgo (delayed, optional), torch._inductor.cpp_builder (conditional), torch._inductor.compile_fx (conditional), torch._inductor.graph (conditional), torch._functorch._aot_autograd.autograd_cache (delayed, optional), torch._inductor.runtime.autotune_cache (delayed, optional), torch._inductor.codecache (conditional), torch._inductor.utils (delayed, optional), torch._dynamo.utils (delayed, conditional, optional)
missing module named 'triton.testing' - imported by torch._inductor.runtime.benchmarking (delayed, optional), torch._inductor.utils (delayed), torch._utils_internal (delayed, conditional)
missing module named 'ck4inductor.universal_gemm' - imported by torch._inductor.codegen.rocm.ck_universal_gemm_template (delayed, optional), torch._inductor.utils (delayed, optional)
missing module named ck4inductor - imported by torch._inductor.codegen.rocm.ck_universal_gemm_template (delayed, optional), torch._inductor.utils (delayed, optional), torch._inductor.codegen.rocm.ck_conv_template (optional)
missing module named colorama - imported by torch._inductor.runtime.runtime_utils (optional)
missing module named halide - imported by torch._inductor.codecache (delayed, conditional), torch._inductor.runtime.halide_helpers (optional)
missing module named rfe - imported by torch._inductor.remote_cache (conditional)
missing module named redis - imported by torch._inductor.remote_cache (optional)
missing module named 'torch._C._dynamo' - imported by torch._dynamo.guards (top-level), torch._dynamo.eval_frame (top-level), torch._dynamo.types (top-level), torch._dynamo.convert_frame (top-level), torch._inductor.fx_passes.reinplace (top-level), torch._functorch._aot_autograd.input_output_analysis (top-level), torch._dynamo.decorators (conditional)
missing module named torch._inductor.fx_passes.fb - imported by torch._inductor.fx_passes (delayed, conditional), torch._inductor.fx_passes.pre_grad (delayed, conditional)
missing module named deeplearning - imported by torch._inductor.fx_passes.group_batch_fusion (optional)
missing module named 'triton.fb' - imported by torch._inductor.cpp_builder (conditional), torch._inductor.codecache (conditional)
missing module named 'libfb.py' - imported by torch._inductor.compile_worker.subproc_pool (delayed, conditional), torch._inductor.codegen.rocm.compile_command (delayed, conditional), torch._dynamo.debug_utils (conditional), torch._inductor.codecache (delayed, conditional)
missing module named 'ck4inductor.grouped_conv_fwd' - imported by torch._inductor.codegen.rocm.ck_conv_template (conditional)
missing module named 'cutlass_library.gemm_operation' - imported by torch._inductor.codegen.cuda.gemm_template (delayed), torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions (conditional)
missing module named 'cutlass_library.library' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional), torch._inductor.codegen.cuda.gemm_template (delayed), torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions (conditional)
missing module named cutlass_generator - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named 'cutlass_library.manifest' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named 'cutlass_library.generator' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named cutlass_library - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, optional)
missing module named cutlass - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, optional)
missing module named 'triton._utils' - imported by torch._higher_order_ops.triton_kernel_wrap (delayed, conditional)
missing module named 'triton._C' - imported by torch._higher_order_ops.triton_kernel_wrap (conditional)
missing module named pygraphviz - imported by networkx.drawing.nx_agraph (delayed, optional)
missing module named 'matplotlib.cm' - imported by networkx.drawing.nx_pylab (delayed)
missing module named 'matplotlib.colors' - imported by networkx.drawing.nx_pylab (delayed)
missing module named 'matplotlib.patches' - imported by networkx.drawing.nx_pylab (delayed)
missing module named 'matplotlib.path' - imported by networkx.drawing.nx_pylab (delayed)
missing module named 'lxml.etree' - imported by networkx.readwrite.graphml (delayed, optional), imageio.plugins._tifffile (delayed, optional)
missing module named 'torch_xla.stablehlo' - imported by torch._functorch.fx_minifier (delayed)
missing module named 'torch.utils._config_typing' - imported by torch._inductor.config (conditional), torch._functorch.config (conditional), torch._dynamo.config (conditional)
missing module named foo - imported by torch._functorch.compilers (delayed)
missing module named torchrec - imported by torch._dynamo.variables.user_defined (delayed)
missing module named '_pytest.recwarn' - imported by torch._dynamo.variables.user_defined (delayed, optional)
missing module named _pytest - imported by torch._dynamo.variables.user_defined (delayed, optional)
missing module named torch._dynamo.variables.symbolic_convert - imported by torch._dynamo.variables.base (conditional)
missing module named 'torch_xla.core' - imported by torch._dynamo.testing (delayed, conditional), torch._dynamo.backends.torchxla (delayed, optional)
missing module named 'optree._C' - imported by torch._dynamo.polyfills.pytree (conditional)
missing module named 'einops._torch_specific' - imported by torch._dynamo.decorators (delayed, conditional, optional)
missing module named einops - imported by torch._dynamo.decorators (delayed, conditional)
missing module named 'tvm.contrib' - imported by torch._dynamo.backends.tvm (delayed)
missing module named tvm - imported by torch._dynamo.backends.tvm (delayed, conditional)
missing module named 'com.sun' - imported by torch._appdirs (delayed, conditional, optional)
missing module named com - imported by torch._appdirs (delayed)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named libfb - imported by torch._inductor.config (conditional, optional)
missing module named 'torch._C._VariableFunctions' - imported by torch (conditional)
missing module named 'torch_xla.utils' - imported by torch._tensor (delayed, conditional)
missing module named torch.randperm - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.Generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.default_generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.ScriptObject - imported by torch (delayed), torch.export.graph_signature (delayed)
missing module named torch.norm_except_dim - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch._weight_norm - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch.trunc - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.square - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sqrt - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.signbit - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sign - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.round - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.reciprocal - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.rad2deg - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.negative - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.logical_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log1p - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log10 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isnan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isinf - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isfinite - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.floor - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.expm1 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.deg2rad - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.conj_physical - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.ceil - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.bitwise_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.absolute - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.true_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.subtract - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.remainder - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.pow - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.not_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.nextafter - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.multiply - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.minimum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.maximum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.ldexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.lcm - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.hypot - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.heaviside - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.gcd - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmod - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmin - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmax - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.floor_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.float_power - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.eq - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.copysign - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_right_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_left_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.arctan2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.add - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.broadcast_shapes - imported by torch (top-level), torch._numpy._funcs_impl (top-level)
missing module named torch.Size - imported by torch (top-level), torch.types (top-level), torch.nn.modules.normalization (top-level)
missing module named torch.qscheme - imported by torch (top-level), torch.types (top-level)
missing module named torch.layout - imported by torch (top-level), torch.types (top-level)
missing module named torch.DispatchKey - imported by torch (top-level), torch.types (top-level)
missing module named torch.device - imported by torch (top-level), torch.types (top-level), torch.nn.modules.module (top-level), torch._library.infer_schema (top-level), torch.cuda (top-level), torch._inductor.graph (top-level), torch.distributed.nn.api.remote_module (top-level), torch.xpu (top-level), torch.cpu (top-level), torch.mtia (top-level)
missing module named _suggestions - imported by traceback (delayed, optional)
missing module named fontTools - imported by pymupdf.utils (delayed, optional)
missing module named cppyy - imported by pymupdf (delayed, conditional)
missing module named pymupdf_fonts - imported by pymupdf (delayed, conditional, optional)
missing module named mupdf - imported by pymupdf (conditional, optional), pymupdf.utils (optional)
missing module named __builtin__ - imported by pymupdf.extra (optional), pymupdf.mupdf (optional)
missing module named mupdf_cppyy - imported by pymupdf (conditional)
missing module named shapely.has_z - imported by shapely (delayed), shapely._ragged_array (delayed)
missing module named shapely.has_m - imported by shapely (delayed), shapely._ragged_array (delayed)
missing module named 'matplotlib.widgets' - imported by tifffile.tifffile (delayed)
missing module named 'zarr.core' - imported by tifffile.zarr (delayed, conditional, optional)
missing module named 'zarr.abc' - imported by tifffile.zarr (optional)
missing module named zarr - imported by tifffile.zarr (top-level)
missing module named _imagecodecs - imported by tifffile.tifffile (delayed, conditional, optional)
missing module named imagecodecs - imported by tifffile.tifffile (optional), imageio.plugins._tifffile (delayed, conditional, optional)
missing module named compression - imported by tifffile._imagecodecs (delayed, optional)
missing module named SimpleITK - imported by skimage.io._plugins.simpleitk_plugin (optional), imageio.plugins.simpleitk (delayed, optional)
missing module named skimage.color.rgba2rgb - imported by skimage.color (delayed, conditional), skimage.exposure.exposure (delayed, conditional)
missing module named skimage.color.rgb2gray - imported by skimage.color (delayed, conditional), skimage.exposure.exposure (delayed, conditional)
missing module named skimage.exposure.is_low_contrast - imported by skimage.exposure (top-level), skimage.io._io (top-level), skimage.io._plugins.matplotlib_plugin (top-level)
missing module named numpydoc - imported by skimage._shared.utils (delayed, optional)
missing module named 'matplotlib.image' - imported by skimage.io._plugins.matplotlib_plugin (delayed)
missing module named 'mpl_toolkits.axes_grid1' - imported by skimage.io._plugins.matplotlib_plugin (delayed)
missing module named imread - imported by skimage.io._plugins.imread_plugin (optional)
missing module named itk - imported by imageio.plugins.simpleitk (delayed, optional)
missing module named rawpy - imported by imageio.plugins.rawpy (top-level)
missing module named 'av.codec' - imported by imageio.plugins.pyav (top-level)
missing module named 'av.filter' - imported by imageio.plugins.pyav (top-level)
missing module named pillow_heif - imported by imageio.plugins.pillow (delayed, optional)
missing module named 'osgeo.gdal' - imported by imageio.plugins.gdal (delayed, optional)
missing module named 'astropy.io' - imported by imageio.plugins.fits (delayed, optional)
missing module named imageio_ffmpeg - imported by imageio.plugins.ffmpeg (top-level)
missing module named tkFileDialog - imported by imageio.plugins._tifffile (delayed, optional)
missing module named Tkinter - imported by imageio.plugins._tifffile (delayed, optional)
missing module named tifffile_geodb - imported by imageio.plugins._tifffile (delayed, optional)
missing module named imageio.plugins.tifffile_geodb - imported by imageio.plugins._tifffile (delayed, optional)
missing module named zstd - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named 'backports.lzma' - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named bsdf_cli - imported by imageio.plugins._bsdf (conditional)
missing module named imp - imported by imageio.core.util (delayed, conditional)
missing module named osgeo - imported by skimage.io._plugins.gdal_plugin (optional)
missing module named astropy - imported by skimage.io._plugins.fits_plugin (optional)
missing module named pathlib2 - imported by easyocr.easyocr (conditional)
missing module named 'six.moves' - imported by easyocr.easyocr (conditional)
missing module named six - imported by easyocr.utils (conditional)
missing module named chardet - imported by requests (optional)
