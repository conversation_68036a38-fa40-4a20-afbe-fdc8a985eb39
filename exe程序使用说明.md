# 发票处理程序 - 独立exe版本使用说明

## 🎉 打包成功！

您的发票处理程序已成功打包为独立的exe文件：

**文件位置**：`dist\InvoiceProcessor.exe`  
**文件大小**：244MB  
**运行环境**：Windows 7/8/10/11（64位）

## 📋 功能特性

### ✅ 完全本地化
- 无需安装Python环境
- 无需配置虚拟环境
- 无需网络连接（首次运行后）
- 单文件部署，即拷即用

### ✅ 智能发票识别
- **发票类型**：自动识别增值税专用/普通发票
- **金额信息**：准确提取小写金额和价税合计
- **税率信息**：直接从发票识别税率（非计算得出）
- **税额信息**：精确识别税额
- **销售方信息**：自动提取销售方名称
- **备注信息**：提取银行账号等信息

### ✅ 容错处理
- 处理OCR识别错误（如：羊→￥，僧值税→增值税）
- 智能税率识别（如：135→13%）
- 多模式信息匹配

## 🚀 使用方法

### 1. 部署到目标电脑
```
1. 将 InvoiceProcessor.exe 复制到目标电脑
2. 双击运行即可（无需安装）
```

### 2. 首次运行
```
⚠️ 重要：首次运行需要网络连接
- 程序会自动下载OCR模型文件（约200MB）
- 下载完成后显示"EasyOCR初始化完成！"
- 之后可完全离线使用
```

### 3. 正常使用
```
1. 启动程序后会显示GUI界面
2. 配置发票文件夹路径
3. 设置关键词匹配规则
4. 点击"开始处理"
5. 程序自动识别并生成报销表
```

## 📊 识别效果

基于实际测试，程序可准确识别：

```
✅ 发票类型：增值税普通发票
✅ 金额：239,121.57元
✅ 税率：13%（直接识别，非计算）
✅ 税额：27,509.55元
✅ 销售方：揭阳天芙新能源科技有限公司
✅ 备注：银行账号等完整信息
```

## ⚠️ 注意事项

### 系统要求
- Windows 7/8/10/11（64位）
- 至少4GB内存（推荐8GB）
- 约500MB可用磁盘空间

### 首次运行
- **必须有网络连接**用于下载OCR模型
- 下载过程可能需要5-10分钟
- 下载完成后可完全离线使用

### 性能说明
- 使用CPU进行OCR识别
- 处理速度：约15-20秒/发票
- 建议一次处理不超过50张发票

### 文件格式支持
- PDF发票文件
- 图片格式：JPG、PNG、BMP等

## 🔧 故障排除

### 常见问题

1. **程序无法启动**
   - 检查是否为64位Windows系统
   - 尝试以管理员身份运行

2. **首次运行卡住**
   - 检查网络连接
   - 等待模型下载完成（可能需要10分钟）

3. **识别结果不准确**
   - 确保发票图片清晰
   - 避免扫描件的二次压缩
   - 检查发票是否为标准格式

4. **内存不足**
   - 关闭其他占用内存的程序
   - 分批处理发票文件

### 获取帮助
如遇到其他问题：
1. 检查程序输出的错误信息
2. 确保发票文件完整且可读
3. 重启程序重试

## 📈 版本信息

- **版本**：2.0（独立exe版本）
- **打包日期**：2025年6月18日
- **OCR引擎**：EasyOCR 1.7.2
- **PDF处理**：PyMuPDF 1.26.0
- **文件大小**：244MB

## 🎯 技术优势

1. **完全本地化**：无需云端API，保护数据隐私
2. **高准确率**：针对中文发票优化的识别算法
3. **容错能力强**：智能处理OCR识别错误
4. **部署简单**：单文件部署，无依赖
5. **离线运行**：首次下载后可完全离线使用

## 📞 技术支持

程序基于以下开源技术构建：
- EasyOCR：本地OCR识别引擎
- PyMuPDF：PDF处理库
- PyInstaller：Python程序打包工具

---

**总结**：这是一个完全独立的发票处理程序，可以在任何Windows电脑上运行，无需安装Python或其他依赖。首次运行需要网络下载模型，之后可完全离线使用，识别准确率高，特别适合企业内部部署使用。
