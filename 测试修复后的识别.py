#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的发票识别效果
"""

import os
import sys

# 添加当前目录到路径
sys.path.append('.')

# 导入修复后的识别函数
from fppl_23_完整集成Surya_OCR import recognize_invoice_surya

def test_invoice_recognition():
    """测试发票识别效果"""
    try:
        print("测试修复后的发票识别效果")
        print("="*60)
        
        # 测试文件夹
        test_folder = r"D:\vscode project\发票处理\1\202501顺洋、来宁、锦阳、奥捷、奥源-5张"
        
        if not os.path.exists(test_folder):
            print(f"❌ 测试文件夹不存在: {test_folder}")
            return
        
        # 查找PDF文件
        pdf_files = []
        for file in os.listdir(test_folder):
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(test_folder, file))
        
        if not pdf_files:
            print("❌ 未找到PDF文件")
            return
        
        print(f"找到 {len(pdf_files)} 个PDF文件")
        
        # 测试每个PDF文件
        for i, pdf_path in enumerate(pdf_files, 1):
            print(f"\n{'='*60}")
            print(f"测试文件 {i}/{len(pdf_files)}: {os.path.basename(pdf_path)}")
            print(f"{'='*60}")
            
            # 识别发票
            result = recognize_invoice_surya(pdf_path)
            
            if result:
                print("\n✅ 识别成功！提取的信息:")
                print("-" * 40)
                
                # 检查关键字段
                fields_to_check = [
                    ('发票类型', 'InvoiceTypeOrg'),
                    ('小写金额', 'AmountInFiguers'),
                    ('价税合计', 'TotalAmount'),
                    ('商品税率', 'CommodityTaxRate'),
                    ('税额', 'TotalTax'),
                    ('销售方名称', 'SellerName'),
                    ('备注', 'Remarks')
                ]
                
                success_count = 0
                for field_name, field_key in fields_to_check:
                    value = result.get(field_key, '')
                    if value:
                        print(f"✅ {field_name}: {value}")
                        success_count += 1
                    else:
                        print(f"❌ {field_name}: 未识别")
                
                print(f"\n识别成功率: {success_count}/{len(fields_to_check)} ({success_count/len(fields_to_check)*100:.1f}%)")
                
                # 检查关键词匹配
                seller_name = result.get('SellerName', '')
                keywords = ['顺洋', '来宁', '锦阳', '奥捷', '奥源']
                matched_keywords = [kw for kw in keywords if kw in seller_name]
                
                if matched_keywords:
                    print(f"✅ 匹配的关键词: {matched_keywords}")
                else:
                    print(f"❌ 未匹配到关键词，销售方名称: {seller_name}")
                
            else:
                print("❌ 识别失败")
        
        print(f"\n{'='*60}")
        print("测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_invoice_recognition()
    input("\n按回车键退出...")
