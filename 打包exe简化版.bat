@echo off
echo 开始打包发票处理程序为exe文件（简化版）...
echo.

REM 激活虚拟环境
call venv\Scripts\activate.bat

REM 检查是否成功激活虚拟环境
if "%VIRTUAL_ENV%"=="" (
    echo 错误：无法激活虚拟环境
    pause
    exit /b 1
)

echo 虚拟环境已激活: %VIRTUAL_ENV%
echo.

echo 正在使用PyInstaller打包程序...
echo 注意：首次运行时会自动下载OCR模型（约200MB）
echo.

REM 使用PyInstaller打包为单个exe文件（简化版）
pyinstaller --onefile ^
    --windowed ^
    --name "发票处理程序" ^
    --hidden-import=easyocr ^
    --hidden-import=fitz ^
    --hidden-import=cv2 ^
    --hidden-import=PIL ^
    --hidden-import=torch ^
    --hidden-import=numpy ^
    --hidden-import=requests ^
    --hidden-import=win32com.client ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.filedialog ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=tkinter.messagebox ^
    --exclude-module=matplotlib ^
    --exclude-module=scipy ^
    --exclude-module=pandas ^
    "fppl-21.尝试替代百度识别.py"

if errorlevel 1 (
    echo.
    echo 打包失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo 打包完成！
echo 可执行文件位置: dist\发票处理程序.exe
echo.

REM 检查文件是否存在
if exist "dist\发票处理程序.exe" (
    echo 文件信息:
    dir "dist\发票处理程序.exe" | find ".exe"
    echo.
    echo ✅ 打包成功！
    echo.
    echo 使用说明：
    echo 1. 将 "发票处理程序.exe" 复制到目标电脑
    echo 2. 首次运行时会自动下载OCR模型（需要网络）
    echo 3. 下载完成后即可离线使用
    echo.
) else (
    echo ❌ 警告：未找到生成的exe文件
)

pause
