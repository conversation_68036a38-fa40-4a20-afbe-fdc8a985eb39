#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
备用EasyOCR版本 - 如果Surya OCR太慢可以用这个
"""

import win32com.client
from datetime import datetime
import os
import warnings
import re
from PIL import Image
import fitz  # PyMuPDF
import easyocr
import cv2
import numpy as np

# 忽略警告
warnings.filterwarnings("ignore")

def enhance_image_for_ocr(image_path):
    """图像预处理以提高OCR识别率"""
    try:
        # 读取图像
        img = cv2.imread(image_path)
        if img is None:
            return image_path
        
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 适度放大图像提高分辨率
        height, width = gray.shape
        scale_factor = 3.0
        high_res = cv2.resize(gray, (int(width * scale_factor), int(height * scale_factor)), interpolation=cv2.INTER_CUBIC)
        
        # 多步骤图像增强
        # 1. 对比度和亮度增强
        enhanced = cv2.convertScaleAbs(high_res, alpha=2.0, beta=40)
        
        # 2. 自适应直方图均衡化
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(enhanced)
        
        # 3. 去噪处理
        enhanced = cv2.fastNlMeansDenoising(enhanced)
        
        # 保存增强后的图像
        enhanced_path = "temp_enhanced_invoice.png"
        cv2.imwrite(enhanced_path, enhanced)
        
        print(f"图像预处理完成: {enhanced_path}")
        return enhanced_path
    except Exception as e:
        print(f"图像预处理失败: {str(e)}")
        return image_path

def pdf_to_image_pymupdf(pdf_path):
    """使用PyMuPDF将PDF转换为高质量图片"""
    try:
        import fitz
        doc = fitz.open(pdf_path)
        page = doc[0]  # 获取第一页
        
        # 设置更高的缩放比例以提高图片质量
        mat = fitz.Matrix(4.0, 4.0)  # 4倍缩放
        pix = page.get_pixmap(matrix=mat)
        
        temp_image_path = "temp_invoice_pymupdf.png"
        pix.save(temp_image_path)
        doc.close()
        
        print(f"PyMuPDF转换完成: {temp_image_path}")
        
        # 对图像进行预处理以提高OCR识别率
        enhanced_path = enhance_image_for_ocr(temp_image_path)
        
        # 清理原始图像
        if enhanced_path != temp_image_path:
            try:
                os.remove(temp_image_path)
            except:
                pass
        
        return enhanced_path
    except Exception as e:
        print(f"PyMuPDF转换失败: {str(e)}")
        return None

def extract_invoice_info_easyocr(text_list):
    """从EasyOCR结果中提取发票信息 - 改进版本"""
    result = {
        'InvoiceTypeOrg': '',
        'AmountInFiguers': '',
        'TotalAmount': '',
        'CommodityTaxRate': '',
        'TotalTax': '',
        'SellerName': '',
        'Remarks': ''
    }

    # 将所有识别的文本保存
    all_text = []
    for bbox, text, confidence in text_list:
        all_text.append((text.strip(), confidence))

    print("开始提取发票信息...")
    
    # 收集所有金额信息
    amounts = []

    # 遍历所有文本进行信息提取
    for i, (line, confidence) in enumerate(all_text):
        line = str(line).strip()
        if not line:
            continue
        
        print(f"{i+1:2d}. 置信度:{confidence:.3f} | 文本: '{line}'")
        
        # 收集金额信息
        amount_patterns = [
            r'[￥¥羊芏](\d+(?:\.\d{2})?)',
            r'(\d{1,6}\.\d{2})',
        ]
        
        for pattern in amount_patterns:
            amount_match = re.search(pattern, line)
            if amount_match:
                try:
                    amount_value = float(amount_match.group(1))
                    if amount_value >= 10:  # 过滤掉太小的数字
                        amounts.append((amount_value, amount_match.group(1), line, i))
                except ValueError:
                    pass

        # 发票类型识别 - 改进识别逻辑
        if not result['InvoiceTypeOrg']:
            # 直接匹配完整的发票类型
            if '增值税专用发票' in line:
                result['InvoiceTypeOrg'] = '增值税专用发票'
                print(f"识别发票类型: {line} -> {result['InvoiceTypeOrg']}")
            elif '增值税普通发票' in line:
                result['InvoiceTypeOrg'] = '增值税普通发票'
                print(f"识别发票类型: {line} -> {result['InvoiceTypeOrg']}")
            # 处理OCR识别错误和分段识别
            elif ('增值税' in line or '僧值税' in line or '橹徒税' in line or '增僮税' in line):
                if '专用' in line:
                    result['InvoiceTypeOrg'] = '增值税专用发票'
                    print(f"识别发票类型(修正OCR): {line} -> {result['InvoiceTypeOrg']}")
                elif '普通' in line:
                    result['InvoiceTypeOrg'] = '增值税普通发票'
                    print(f"识别发票类型(修正OCR): {line} -> {result['InvoiceTypeOrg']}")
            # 如果只找到"电子发票"，默认为普通发票
            elif '电子发票' in line and not any(keyword in line for keyword in ['专用', '普通']):
                # 检查上下文是否有专用发票标识
                context_has_special = False
                for j in range(max(0, i-3), min(len(all_text), i+4)):
                    if j != i and '专用' in all_text[j][0]:
                        context_has_special = True
                        break
                
                if context_has_special:
                    result['InvoiceTypeOrg'] = '增值税专用发票'
                    print(f"识别发票类型(上下文推断): {line} -> 增值税专用发票")
                else:
                    result['InvoiceTypeOrg'] = '增值税普通发票'
                    print(f"识别发票类型(默认): {line} -> 增值税普通发票")

        # 税率识别 - 直接从发票文本中识别
        if not result['CommodityTaxRate']:
            # 直接匹配百分比
            if re.search(r'\d+%', line):
                tax_rate_match = re.search(r'(\d+(?:\.\d+)?)%', line)
                if tax_rate_match:
                    rate_str = tax_rate_match.group(1)
                    result['CommodityTaxRate'] = [rate_str + '%']
                    print(f"识别税率: {line} -> {rate_str}%")
            
            # 查找税率相关的行
            elif '税率' in line or '征收率' in line:
                print(f"找到税率相关行: {line}")
                # 在上下文中查找百分比
                for j in range(max(0, i-3), min(len(all_text), i+6)):
                    context_line = all_text[j][0]
                    if re.search(r'\d+%', context_line):
                        tax_rate_match = re.search(r'(\d+(?:\.\d+)?)%', context_line)
                        if tax_rate_match:
                            rate_str = tax_rate_match.group(1)
                            result['CommodityTaxRate'] = [rate_str + '%']
                            print(f"识别税率(上下文): {context_line} -> {rate_str}%")
                            break
            
            # 处理OCR错误的特殊情况
            elif line.strip() in ['135', '13', '130']:  # 常见OCR错误
                # 检查上下文是否有税率相关信息（扩大搜索范围）
                context_has_tax = False
                for j in range(max(0, i-10), min(len(all_text), i+11)):
                    if j != i:
                        context_line = all_text[j][0]
                        if '税率' in context_line or '征收率' in context_line:
                            context_has_tax = True
                            print(f"在上下文中找到税率相关: {context_line}")
                            break
                
                if context_has_tax:
                    if line.strip() == '135':
                        result['CommodityTaxRate'] = ['13%']
                        print(f"识别税率(修正OCR错误): {line} -> 13%")
                    elif line.strip() == '13':
                        result['CommodityTaxRate'] = ['13%']
                        print(f"识别税率: {line} -> 13%")
                    elif line.strip() == '130':
                        result['CommodityTaxRate'] = ['13%']
                        print(f"识别税率(修正OCR错误): {line} -> 13%")

        # 销售方名称识别
        if not result['SellerName']:
            # 查找销售方名称
            if '销' in line and '名称' in line:
                # 提取名称部分
                name_match = re.search(r'名称[：:]\s*(.+)', line)
                if name_match:
                    seller_name = name_match.group(1).strip()
                    # 应用OCR错误修正
                    seller_name = correct_company_name_ocr(seller_name)
                    result['SellerName'] = seller_name
                    print(f"识别销售方: {line} -> {seller_name}")
            
            # 如果还没找到销售方，查找包含公司名称的行
            elif ('公司' in line or '有限' in line) and len(line) > 5:
                # 排除购方信息
                if '购' not in line and '电网' not in line and '供电局' not in line:
                    # 常见的销售方公司名称模式
                    if any(keyword in line for keyword in ['科技', '新能源', '实业', '贸易', '工程', '建设']):
                        # 强化OCR错误修正
                        corrected_name = line.strip()
                        
                        # 修正常见的OCR错误
                        ocr_corrections = {
                            '锦阴': '锦阳', '揭阴': '揭阳', '夭美': '天美', '广氽': '广东',
                            '樊捷': '奥捷', '樊源': '奥源', '来亍': '来宁', '來亍': '来宁',
                            '奧捷': '奥捷', '奧源': '奥源', '澳捷': '奥捷', '澳源': '奥源',
                            '敖捷': '奥捷', '敖源': '奥源', '順洋': '顺洋', '顺羊': '顺洋'
                        }
                        
                        for error, correct in ocr_corrections.items():
                            if error in corrected_name:
                                corrected_name = corrected_name.replace(error, correct)
                        
                        result['SellerName'] = corrected_name
                        if corrected_name != line.strip():
                            print(f"识别销售方(OCR修正): {line} -> {corrected_name}")
                        else:
                            print(f"识别销售方(公司名): {line}")

        # 备注信息识别
        if not result['Remarks']:
            if '银行账号' in line or '开户银行' in line:
                result['Remarks'] = line.strip()
                print(f"识别备注: {line}")

    # 智能分析金额信息
    if amounts:
        amounts.sort(reverse=True)  # 按金额从大到小排序
        print(f"\n发现的金额信息: {[f'{amt}({text})' for amt, text, _, _ in amounts]}")
        
        # 价税合计通常是最大的金额
        if not result['TotalAmount'] and amounts:
            result['TotalAmount'] = amounts[0][1]
            print(f"识别价税合计(最大金额): {amounts[0][2]} -> {amounts[0][1]}")
        
        # 小写金额通常是第二大的金额（不含税金额）
        if not result['AmountInFiguers'] and len(amounts) >= 2:
            result['AmountInFiguers'] = amounts[1][1]
            print(f"识别小写金额(第二大金额): {amounts[1][2]} -> {amounts[1][1]}")
        
        # 税额通常是最小的金额
        if not result['TotalTax'] and len(amounts) >= 2:
            # 查找最小的合理税额
            for amt, text, line, _ in reversed(amounts):
                if amt >= 10:  # 税额至少10元
                    result['TotalTax'] = text
                    print(f"识别税额(最小合理金额): {line} -> {text}")
                    break

    print(f"\n发票信息提取完成:")
    for key, value in result.items():
        print(f"  {key}: {value}")

    return result

def correct_company_name_ocr(text):
    """OCR错误修正"""
    corrections = {
        '奥捷': ['樊捷', '奧捷', '澳捷', '敖捷', '嗷捷'],
        '奥源': ['樊源', '奧源', '澳源', '敖源', '嗷源'],
        '来宁': ['来亍', '來宁', '来寧', '來寧', '来宇'],
        '锦阳': ['锦阴', '錦阳', '锦陽', '錦陽'],
        '顺洋': ['順洋', '顺羊', '順羊', '顺样'],
        '揭阳': ['揭阴', '揭陽', '揭陰', '揭样'],
        '广东': ['广氽', '廣东', '廣氽', '广束'],
        '天美': ['夭美', '天羙', '夭羙', '天关'],
    }
    
    corrected_text = text
    for correct, errors in corrections.items():
        for error in errors:
            if error in corrected_text:
                corrected_text = corrected_text.replace(error, correct)
                print(f"    OCR修正: '{error}' -> '{correct}'")
    
    return corrected_text

def recognize_invoice_easyocr(file_path):
    """使用EasyOCR识别发票信息"""
    print(f"开始处理发票: {file_path}")
    
    temp_image_path = None
    try:
        # 如果是PDF，转换为图片
        if file_path.lower().endswith('.pdf'):
            print("正在转换PDF为图片...")
            temp_image_path = pdf_to_image_pymupdf(file_path)
            
            if not temp_image_path:
                print("PDF转换失败")
                return None
            
            file_path = temp_image_path
            print("PDF转换完成")
        
        # 初始化EasyOCR（单例模式）
        if not hasattr(recognize_invoice_easyocr, 'ocr'):
            print("正在初始化EasyOCR...")
            recognize_invoice_easyocr.ocr = easyocr.Reader(['ch_sim', 'en'], gpu=False)
            print("EasyOCR初始化完成！")
        
        # 使用EasyOCR识别图片
        print("正在进行OCR识别...")
        result = recognize_invoice_easyocr.ocr.readtext(file_path)
        
        if not result:
            print("OCR识别结果为空")
            return None
        
        print(f"OCR识别到 {len(result)} 个文本区域")
        
        # 从OCR结果中提取发票信息
        invoice_data = extract_invoice_info_easyocr(result)
        return invoice_data
        
    except Exception as e:
        print(f"发票识别失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        if temp_image_path and os.path.exists(temp_image_path):
            try:
                os.remove(temp_image_path)
                print("临时文件已清理")
            except:
                pass

def test_easyocr_processing():
    """测试EasyOCR处理"""
    try:
        print("测试EasyOCR发票处理")
        print("="*60)
        
        # 使用您提供的文件路径
        invoice_folder = r"D:\vscode project\发票处理\1\202501顺洋、来宁、锦阳、奥捷、奥源-5张"
        keywords = ["锦阳", "顺洋", "来宁", "奥捷", "奥源"]
        
        print(f"发票文件夹: {invoice_folder}")
        print(f"关键词: {keywords}")
        print("="*60)
        
        if not os.path.exists(invoice_folder):
            print(f"❌ 发票文件夹不存在: {invoice_folder}")
            return
        
        # 查找PDF文件
        pdf_files = []
        for file in os.listdir(invoice_folder):
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(invoice_folder, file))
        
        if not pdf_files:
            print("❌ 未找到PDF文件")
            return
        
        print(f"找到 {len(pdf_files)} 个PDF文件")
        
        # 为每个关键词查找匹配的发票
        pdf_paths_data = {}
        for keyword in keywords:
            print(f"\n查找关键词 '{keyword}' 的匹配发票...")
            
            for pdf_path in pdf_files:
                try:
                    # 使用EasyOCR识别发票
                    invoice_data = recognize_invoice_easyocr(pdf_path)
                    if invoice_data:
                        # 获取销售方名称
                        seller_name = invoice_data.get('SellerName', '')
                        # 检查关键词是否在销售方名称中
                        if keyword in seller_name:
                            pdf_paths_data[keyword] = (pdf_path, invoice_data)
                            print(f"✅ 找到匹配发票: {os.path.basename(pdf_path)}")
                            break
                except Exception as e:
                    print(f"处理发票 {os.path.basename(pdf_path)} 时出错: {str(e)}")
                    continue
            
            if keyword not in pdf_paths_data:
                print(f"❌ 未找到关键词 '{keyword}' 的匹配发票")
        
        print(f"\n总共找到 {len(pdf_paths_data)} 个匹配的发票")
        
        # 显示匹配结果
        for keyword, (pdf_path, invoice_data) in pdf_paths_data.items():
            print(f"\n关键词 '{keyword}' 匹配结果:")
            print(f"  文件: {os.path.basename(pdf_path)}")
            print(f"  发票类型: {invoice_data.get('InvoiceTypeOrg', '未识别')}")
            print(f"  小写金额: {invoice_data.get('AmountInFiguers', '未识别')}")
            print(f"  价税合计: {invoice_data.get('TotalAmount', '未识别')}")
            print(f"  税率: {invoice_data.get('CommodityTaxRate', '未识别')}")
            print(f"  税额: {invoice_data.get('TotalTax', '未识别')}")
            print(f"  销售方: {invoice_data.get('SellerName', '未识别')}")
        
        print(f"\n{'='*60}")
        print("EasyOCR测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_easyocr_processing()
