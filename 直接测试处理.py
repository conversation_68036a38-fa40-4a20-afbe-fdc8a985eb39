#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试发票处理 - 跳过GUI界面
"""

import win32com.client
from datetime import datetime
import os
import warnings
import re
from PIL import Image
import fitz  # PyMuPDF

# Surya OCR imports
from surya.recognition import RecognitionPredictor
from surya.detection import DetectionPredictor

# 忽略警告
warnings.filterwarnings("ignore")

class SuryaOCRProcessor:
    """Surya OCR处理器 - 高精度OCR识别"""
    
    def __init__(self):
        """初始化Surya OCR模型"""
        print("正在初始化Surya OCR模型...")
        
        try:
            # 初始化检测和识别预测器
            print("  加载文本检测模型...")
            self.detection_predictor = DetectionPredictor()
            
            print("  加载文本识别模型...")
            self.recognition_predictor = RecognitionPredictor()
            
            print("✅ Surya OCR初始化完成！")
            
        except Exception as e:
            print(f"❌ Surya OCR初始化失败: {str(e)}")
            raise e
    
    def recognize_image(self, image_path):
        """使用Surya OCR识别图像中的文本"""
        try:
            print(f"使用Surya OCR识别图像: {image_path}")
            
            # 加载图像
            image = Image.open(image_path)
            
            # 运行OCR识别
            predictions = self.recognition_predictor([image], det_predictor=self.detection_predictor)
            
            # 转换为标准格式 (bbox, text, confidence)
            result = []
            if predictions and len(predictions) > 0:
                page_prediction = predictions[0]
                for text_line in page_prediction.text_lines:
                    # 获取边界框坐标
                    bbox = text_line.bbox
                    text = text_line.text
                    confidence = text_line.confidence
                    
                    # 转换为标准格式: (bbox, text, confidence)
                    result.append((bbox, text, confidence))
            
            print(f"Surya OCR识别完成，共识别到 {len(result)} 个文本区域")
            return result
            
        except Exception as e:
            print(f"Surya OCR识别失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return []

def pdf_to_image_pymupdf(pdf_path):
    """使用PyMuPDF将PDF转换为高质量图片"""
    try:
        import fitz
        doc = fitz.open(pdf_path)
        page = doc[0]  # 获取第一页
        
        # 设置高分辨率以提高OCR识别率
        mat = fitz.Matrix(3.0, 3.0)  # 3倍缩放
        pix = page.get_pixmap(matrix=mat)
        
        temp_image_path = "temp_invoice_surya.png"
        pix.save(temp_image_path)
        doc.close()
        
        print(f"PDF转换完成: {temp_image_path}")
        return temp_image_path
    except Exception as e:
        print(f"PDF转换失败: {str(e)}")
        return None

def extract_invoice_info_surya(ocr_result):
    """从Surya OCR结果中提取发票信息 - 修复版本"""
    result = {
        'InvoiceTypeOrg': '',           # 发票类型
        'AmountInFiguers': '',          # 小写金额
        'TotalAmount': '',              # 价税合计
        'CommodityTaxRate': '',         # 商品税率
        'TotalTax': '',                 # 税额
        'SellerName': '',               # 销售方名称
        'Remarks': ''                   # 备注
    }
    
    # 将OCR结果转换为文本列表，便于处理
    all_text = [(text, confidence) for _, text, confidence in ocr_result]
    
    print(f"\n开始提取发票信息，共 {len(all_text)} 行文本")
    
    # 收集所有金额信息，用于智能判断
    amounts = []
    
    for i, (line, confidence) in enumerate(all_text):
        line = str(line).strip()
        if not line:
            continue
        
        print(f"{i+1:2d}. 置信度:{confidence:.3f} | 文本: '{line}'")
        
        # 收集所有金额信息
        amount_match = re.search(r'¥(\d+(?:\.\d{2})?)', line)
        if amount_match:
            try:
                amount_value = float(amount_match.group(1))
                amounts.append((amount_value, amount_match.group(1), line, i))
            except ValueError:
                pass
        
        # 发票类型识别
        if not result['InvoiceTypeOrg']:
            if '增值税专用发票' in line:
                result['InvoiceTypeOrg'] = '增值税专用发票'
                print(f"✅ 识别发票类型: {line}")
            elif '增值税普通发票' in line:
                result['InvoiceTypeOrg'] = '增值税普通发票'
                print(f"✅ 识别发票类型: {line}")
            elif '电子发票' in line:
                # 检查上下文确定是专用还是普通发票
                context_has_special = any('专用' in all_text[j][0] 
                                        for j in range(max(0, i-3), min(len(all_text), i+4)))
                if context_has_special:
                    result['InvoiceTypeOrg'] = '增值税专用发票'
                else:
                    result['InvoiceTypeOrg'] = '增值税普通发票'
                print(f"✅ 识别发票类型(上下文推断): {result['InvoiceTypeOrg']}")
        
        # 税率识别 - 改进的识别逻辑
        if not result['CommodityTaxRate']:
            # 直接匹配百分比格式的税率
            if re.search(r'\d+%', line):
                tax_rate_match = re.search(r'(\d+(?:\.\d+)?)%', line)
                if tax_rate_match:
                    rate_str = tax_rate_match.group(1)
                    result['CommodityTaxRate'] = [rate_str + '%']
                    print(f"✅ 识别税率: {line} -> {rate_str}%")
        
        # 销售方名称识别
        if not result['SellerName']:
            # 查找销售方名称
            if '销' in line and '名称' in line:
                # 提取名称部分
                name_match = re.search(r'名称[：:]\s*(.+)', line)
                if name_match:
                    seller_name = name_match.group(1).strip()
                    # 应用OCR错误修正
                    seller_name = correct_company_name_ocr(seller_name)
                    result['SellerName'] = seller_name
                    print(f"✅ 识别销售方: {line} -> {seller_name}")
            
            # 如果还没找到销售方，查找包含公司名称的行
            elif ('公司' in line or '有限' in line) and len(line) > 5:
                # 排除购方信息
                if '购' not in line and '电网' not in line and '供电局' not in line:
                    # 常见的销售方公司名称模式
                    if any(keyword in line for keyword in ['科技', '新能源', '实业', '贸易', '工程', '建设']):
                        corrected_name = correct_company_name_ocr(line.strip())
                        result['SellerName'] = corrected_name
                        if corrected_name != line.strip():
                            print(f"✅ 识别销售方(OCR修正): {line} -> {corrected_name}")
                        else:
                            print(f"✅ 识别销售方: {line}")
        
        # 备注信息识别
        if not result['Remarks']:
            if '银行账号' in line or '开户银行' in line:
                result['Remarks'] = line.strip()
                print(f"✅ 识别备注: {line}")
    
    # 智能分析金额信息
    if amounts:
        amounts.sort(reverse=True)  # 按金额从大到小排序
        print(f"\n发现的金额信息: {[f'{amt}({text})' for amt, text, _, _ in amounts]}")
        
        # 价税合计通常是最大的金额
        if not result['TotalAmount'] and amounts:
            result['TotalAmount'] = amounts[0][1]
            print(f"✅ 识别价税合计(最大金额): {amounts[0][2]} -> {amounts[0][1]}")
        
        # 小写金额通常是第二大的金额（不含税金额）
        if not result['AmountInFiguers'] and len(amounts) >= 2:
            result['AmountInFiguers'] = amounts[1][1]
            print(f"✅ 识别小写金额(第二大金额): {amounts[1][2]} -> {amounts[1][1]}")
        
        # 税额通常是最小的金额
        if not result['TotalTax'] and len(amounts) >= 2:
            # 查找最小的合理税额
            for amt, text, line, _ in reversed(amounts):
                if amt >= 10:  # 税额至少10元
                    result['TotalTax'] = text
                    print(f"✅ 识别税额(最小合理金额): {line} -> {text}")
                    break
    
    print(f"\n发票信息提取完成:")
    for key, value in result.items():
        print(f"  {key}: {value}")
    
    return result

def correct_company_name_ocr(text):
    """OCR错误修正 - 针对公司名称的常见错误"""
    corrections = {
        # 公司名称修正
        '奥捷': ['樊捷', '奧捷', '澳捷', '敖捷', '嗷捷'],
        '奥源': ['樊源', '奧源', '澳源', '敖源', '嗷源'],
        '来宁': ['来亍', '來宁', '来寧', '來寧', '来宇'],
        '锦阳': ['锦阴', '錦阳', '锦陽', '錦陽', '锦阴'],
        '顺洋': ['順洋', '顺羊', '順羊', '顺样'],
        '揭阳': ['揭阴', '揭陽', '揭陰', '揭样'],
        '广东': ['广氽', '廣东', '廣氽', '广束'],
        '天美': ['夭美', '天羙', '夭羙', '天关'],
    }
    
    corrected_text = text
    for correct, errors in corrections.items():
        for error in errors:
            if error in corrected_text:
                corrected_text = corrected_text.replace(error, correct)
                print(f"    OCR修正: '{error}' -> '{correct}'")
    
    return corrected_text

def recognize_invoice_surya(file_path):
    """使用Surya OCR识别发票信息"""
    print(f"开始处理发票: {file_path}")
    
    temp_image_path = None
    try:
        # 如果是PDF，转换为图片
        if file_path.lower().endswith('.pdf'):
            print("正在转换PDF为图片...")
            temp_image_path = pdf_to_image_pymupdf(file_path)
            
            if not temp_image_path:
                print("PDF转换失败")
                return None
            
            file_path = temp_image_path
            print("PDF转换完成")
        
        # 初始化Surya OCR处理器（单例模式）
        if not hasattr(recognize_invoice_surya, 'ocr_processor'):
            recognize_invoice_surya.ocr_processor = SuryaOCRProcessor()
        
        # 使用Surya OCR识别图片
        print("正在进行OCR识别...")
        result = recognize_invoice_surya.ocr_processor.recognize_image(file_path)
        
        if not result:
            print("OCR识别结果为空")
            return None
        
        print(f"OCR识别到 {len(result)} 个文本区域")
        
        # 从OCR结果中提取发票信息
        invoice_data = extract_invoice_info_surya(result)
        print(f"识别结果: {invoice_data}")
        return invoice_data
        
    except Exception as e:
        print(f"发票识别失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        if temp_image_path and os.path.exists(temp_image_path):
            try:
                os.remove(temp_image_path)
                print("临时文件已清理")
            except:
                pass

def test_direct_processing():
    """直接测试处理逻辑"""
    try:
        print("直接测试发票处理 - 跳过GUI")
        print("="*60)
        
        # 使用您提供的文件路径
        template_path = r"D:\vscode project\发票处理\1\模板.xls"
        export_list_path = r"D:\vscode project\发票处理\1\202501导出清单.xlsx"
        invoice_folder = r"D:\vscode project\发票处理\1\202501顺洋、来宁、锦阳、奥捷、奥源-5张"
        keywords = ["锦阳", "顺洋", "来宁", "奥捷", "奥源"]
        
        print(f"模板文件: {template_path}")
        print(f"导出清单: {export_list_path}")
        print(f"发票文件夹: {invoice_folder}")
        print(f"关键词: {keywords}")
        print("="*60)
        
        # 检查文件是否存在
        if not os.path.exists(template_path):
            print(f"❌ 模板文件不存在: {template_path}")
            return
        
        if not os.path.exists(export_list_path):
            print(f"❌ 导出清单不存在: {export_list_path}")
            return
        
        if not os.path.exists(invoice_folder):
            print(f"❌ 发票文件夹不存在: {invoice_folder}")
            return
        
        # 查找PDF文件
        pdf_files = []
        for file in os.listdir(invoice_folder):
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(invoice_folder, file))
        
        if not pdf_files:
            print("❌ 未找到PDF文件")
            return
        
        print(f"找到 {len(pdf_files)} 个PDF文件")
        
        # 为每个关键词查找匹配的发票
        pdf_paths_data = {}
        for keyword in keywords:
            print(f"\n查找关键词 '{keyword}' 的匹配发票...")
            
            for pdf_path in pdf_files:
                try:
                    # 使用Surya OCR识别发票
                    invoice_data = recognize_invoice_surya(pdf_path)
                    if invoice_data:
                        # 获取销售方名称
                        seller_name = invoice_data.get('SellerName', '')
                        # 检查关键词是否在销售方名称中
                        if keyword in seller_name:
                            pdf_paths_data[keyword] = (pdf_path, invoice_data)
                            print(f"✅ 找到匹配发票: {os.path.basename(pdf_path)}")
                            break
                except Exception as e:
                    print(f"处理发票 {os.path.basename(pdf_path)} 时出错: {str(e)}")
                    continue
            
            if keyword not in pdf_paths_data:
                print(f"❌ 未找到关键词 '{keyword}' 的匹配发票")
        
        print(f"\n总共找到 {len(pdf_paths_data)} 个匹配的发票")
        
        # 显示匹配结果
        for keyword, (pdf_path, invoice_data) in pdf_paths_data.items():
            print(f"\n关键词 '{keyword}' 匹配结果:")
            print(f"  文件: {os.path.basename(pdf_path)}")
            print(f"  发票类型: {invoice_data.get('InvoiceTypeOrg', '未识别')}")
            print(f"  小写金额: {invoice_data.get('AmountInFiguers', '未识别')}")
            print(f"  价税合计: {invoice_data.get('TotalAmount', '未识别')}")
            print(f"  税率: {invoice_data.get('CommodityTaxRate', '未识别')}")
            print(f"  税额: {invoice_data.get('TotalTax', '未识别')}")
            print(f"  销售方: {invoice_data.get('SellerName', '未识别')}")
        
        print(f"\n{'='*60}")
        print("测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_direct_processing()
