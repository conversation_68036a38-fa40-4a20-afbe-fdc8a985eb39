#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进OCR识别精度的测试程序
"""

import os
import easyocr
import fitz
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter

def enhance_image_for_ocr(image_path):
    """图像预处理以提高OCR识别率"""
    # 读取图像
    img = cv2.imread(image_path)
    
    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 方法1：高分辨率版本
    height, width = gray.shape
    scale_factor = 3.0  # 放大3倍
    high_res = cv2.resize(gray, (int(width * scale_factor), int(height * scale_factor)), interpolation=cv2.INTER_CUBIC)
    
    # 方法2：对比度增强
    enhanced = cv2.convertScaleAbs(high_res, alpha=1.5, beta=30)
    
    # 方法3：去噪
    denoised = cv2.fastNlMeansDenoising(enhanced)
    
    # 方法4：锐化
    kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(denoised, -1, kernel)
    
    # 保存处理后的图像
    processed_paths = []
    
    # 保存原始放大版本
    path1 = "enhanced_1_high_res.png"
    cv2.imwrite(path1, high_res)
    processed_paths.append(path1)
    
    # 保存对比度增强版本
    path2 = "enhanced_2_contrast.png"
    cv2.imwrite(path2, enhanced)
    processed_paths.append(path2)
    
    # 保存去噪版本
    path3 = "enhanced_3_denoised.png"
    cv2.imwrite(path3, denoised)
    processed_paths.append(path3)
    
    # 保存锐化版本
    path4 = "enhanced_4_sharpened.png"
    cv2.imwrite(path4, sharpened)
    processed_paths.append(path4)
    
    return processed_paths

def multi_ocr_recognition(image_paths):
    """使用多种配置进行OCR识别"""
    # 初始化不同配置的OCR
    readers = [
        easyocr.Reader(['ch_sim', 'en'], gpu=False),  # 标准配置
    ]
    
    all_results = []
    
    for i, image_path in enumerate(image_paths):
        print(f"\n处理图像 {i+1}: {image_path}")
        
        for j, reader in enumerate(readers):
            try:
                print(f"  使用OCR配置 {j+1} 识别...")
                result = reader.readtext(image_path, detail=1, paragraph=False)
                
                print(f"  识别到 {len(result)} 个文本区域")
                
                # 存储结果
                all_results.append({
                    'image': image_path,
                    'config': j+1,
                    'result': result
                })
                
                # 显示前10个结果
                for k, (bbox, text, confidence) in enumerate(result[:10]):
                    print(f"    {k+1:2d}. 置信度:{confidence:.2f} | 文本: '{text}'")
                
            except Exception as e:
                print(f"  OCR配置 {j+1} 识别失败: {str(e)}")
    
    return all_results

def find_best_recognition(all_results, target_keywords):
    """找到最佳识别结果"""
    best_results = {}
    
    for keyword in target_keywords:
        best_match = None
        best_confidence = 0
        
        for result_set in all_results:
            for bbox, text, confidence in result_set['result']:
                if keyword in text and confidence > best_confidence:
                    best_match = {
                        'text': text,
                        'confidence': confidence,
                        'image': result_set['image'],
                        'config': result_set['config']
                    }
                    best_confidence = confidence
        
        best_results[keyword] = best_match
    
    return best_results

def test_improved_ocr():
    """测试改进的OCR识别"""
    try:
        # 查找PDF文件
        pdf_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(root, file))
        
        if not pdf_files:
            print("未找到PDF文件")
            return
        
        test_pdf = pdf_files[0]
        print(f"测试PDF文件: {test_pdf}")
        
        # PDF转图片
        doc = fitz.open(test_pdf)
        page = doc[0]
        
        # 使用更高的分辨率
        mat = fitz.Matrix(4.0, 4.0)  # 4倍缩放
        pix = page.get_pixmap(matrix=mat)
        
        original_image = "original_high_res.png"
        pix.save(original_image)
        doc.close()
        
        print(f"原始高分辨率图片已保存: {original_image}")
        
        # 图像预处理
        print("\n开始图像预处理...")
        enhanced_images = enhance_image_for_ocr(original_image)
        
        # 多OCR识别
        print("\n开始多配置OCR识别...")
        all_images = [original_image] + enhanced_images
        all_results = multi_ocr_recognition(all_images)
        
        # 查找关键信息的最佳识别结果
        target_keywords = ['增值税', '专用发票', '锦阳', '天美', '揭阳']
        best_results = find_best_recognition(all_results, target_keywords)
        
        print("\n" + "="*60)
        print("最佳识别结果:")
        print("="*60)
        
        for keyword, result in best_results.items():
            if result:
                print(f"关键词 '{keyword}':")
                print(f"  最佳匹配: '{result['text']}'")
                print(f"  置信度: {result['confidence']:.2f}")
                print(f"  来源图像: {result['image']}")
                print(f"  OCR配置: {result['config']}")
                print()
            else:
                print(f"关键词 '{keyword}': 未找到匹配")
                print()
        
        # 清理临时文件
        temp_files = [original_image] + enhanced_images
        for temp_file in temp_files:
            try:
                os.remove(temp_file)
            except:
                pass
        
        print("临时文件已清理")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_improved_ocr()
    input("\n按回车键退出...")
