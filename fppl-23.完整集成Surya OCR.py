#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发票处理程序 - 完整集成Surya OCR版本
集成Surya高精度OCR与完整的数据处理流程
"""

import win32com.client
from datetime import datetime
import os
import warnings
import tkinter as tk
from tkinter import filedialog, ttk
from tkinter import messagebox
import re
from PIL import Image
import fitz  # PyMuPDF

# Surya OCR imports
from surya.recognition import RecognitionPredictor
from surya.detection import DetectionPredictor

# 忽略警告
warnings.filterwarnings("ignore")

class SuryaOCRProcessor:
    """Surya OCR处理器 - 高精度OCR识别"""
    
    def __init__(self):
        """初始化Surya OCR模型"""
        print("正在初始化Surya OCR模型...")
        print("Surya OCR: 支持90+语言的高精度文档OCR系统")
        
        try:
            # 初始化检测和识别预测器
            print("  加载文本检测模型...")
            self.detection_predictor = DetectionPredictor()
            
            print("  加载文本识别模型...")
            self.recognition_predictor = RecognitionPredictor()
            
            print("✅ Surya OCR初始化完成！")
            
        except Exception as e:
            print(f"❌ Surya OCR初始化失败: {str(e)}")
            raise e
    
    def recognize_image(self, image_path):
        """使用Surya OCR识别图像中的文本"""
        try:
            print(f"使用Surya OCR识别图像: {image_path}")
            
            # 加载图像
            image = Image.open(image_path)
            
            # 运行OCR识别
            predictions = self.recognition_predictor([image], det_predictor=self.detection_predictor)
            
            # 转换为标准格式 (bbox, text, confidence)
            result = []
            if predictions and len(predictions) > 0:
                page_prediction = predictions[0]
                for text_line in page_prediction.text_lines:
                    # 获取边界框坐标
                    bbox = text_line.bbox
                    text = text_line.text
                    confidence = text_line.confidence
                    
                    # 转换为标准格式: (bbox, text, confidence)
                    result.append((bbox, text, confidence))
            
            print(f"Surya OCR识别完成，共识别到 {len(result)} 个文本区域")
            return result
            
        except Exception as e:
            print(f"Surya OCR识别失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return []

def pdf_to_image_pymupdf(pdf_path):
    """使用PyMuPDF将PDF转换为高质量图片"""
    try:
        import fitz
        doc = fitz.open(pdf_path)
        page = doc[0]  # 获取第一页
        
        # 设置高分辨率以提高OCR识别率
        mat = fitz.Matrix(3.0, 3.0)  # 3倍缩放
        pix = page.get_pixmap(matrix=mat)
        
        temp_image_path = "temp_invoice_surya.png"
        pix.save(temp_image_path)
        doc.close()
        
        print(f"PDF转换完成: {temp_image_path}")
        return temp_image_path
    except Exception as e:
        print(f"PDF转换失败: {str(e)}")
        return None

def extract_invoice_info_surya(ocr_result):
    """从Surya OCR结果中提取发票信息 - 完整版本"""
    result = {
        'InvoiceTypeOrg': '',           # 发票类型
        'AmountInFiguers': '',          # 小写金额
        'TotalAmount': '',              # 价税合计
        'CommodityTaxRate': '',         # 商品税率
        'TotalTax': '',                 # 税额
        'SellerName': '',               # 销售方名称
        'Remarks': ''                   # 备注
    }
    
    # 将OCR结果转换为文本列表，便于处理
    all_text = [(text, confidence) for _, text, confidence in ocr_result]
    
    print(f"\n开始提取发票信息，共 {len(all_text)} 行文本")
    
    for i, (line, confidence) in enumerate(all_text):
        line = str(line).strip()
        if not line:
            continue
        
        print(f"{i+1:2d}. 置信度:{confidence:.3f} | 文本: '{line}'")
        
        # 发票类型识别 - 使用Surya的高精度识别
        if not result['InvoiceTypeOrg']:
            if '增值税专用发票' in line:
                result['InvoiceTypeOrg'] = '增值税专用发票'
                print(f"✅ 识别发票类型: {line}")
            elif '增值税普通发票' in line:
                result['InvoiceTypeOrg'] = '增值税普通发票'
                print(f"✅ 识别发票类型: {line}")
            elif '电子发票' in line:
                # 检查上下文确定是专用还是普通发票
                context_has_special = any('专用' in all_text[j][0] 
                                        for j in range(max(0, i-3), min(len(all_text), i+4)))
                if context_has_special:
                    result['InvoiceTypeOrg'] = '增值税专用发票'
                else:
                    result['InvoiceTypeOrg'] = '增值税普通发票'
                print(f"✅ 识别发票类型(上下文推断): {result['InvoiceTypeOrg']}")
        
        # 税率识别 - 改进的识别逻辑
        if not result['CommodityTaxRate']:
            # 直接匹配百分比格式的税率
            if re.search(r'\d+%', line):
                tax_rate_match = re.search(r'(\d+(?:\.\d+)?)%', line)
                if tax_rate_match:
                    rate_str = tax_rate_match.group(1)
                    result['CommodityTaxRate'] = [rate_str + '%']
                    print(f"✅ 识别税率: {line} -> {rate_str}%")

            # 查找税率相关的行
            elif '税率' in line or '征收率' in line:
                print(f"找到税率相关行: {line}")
                # 检查后续几行是否有税率数字
                for j in range(i+1, min(len(all_text), i+6)):
                    next_line = all_text[j][0].strip()
                    if re.search(r'\d+%', next_line):
                        tax_rate_match = re.search(r'(\d+(?:\.\d+)?)%', next_line)
                        if tax_rate_match:
                            rate_str = tax_rate_match.group(1)
                            result['CommodityTaxRate'] = [rate_str + '%']
                            print(f"✅ 识别税率(后续行): {next_line} -> {rate_str}%")
                            break
        
        # 小写金额识别 (AmountInFiguers) - 改进的识别逻辑
        if not result['AmountInFiguers']:
            # 匹配金额模式，包括¥符号
            amount_patterns = [
                r'¥(\d+(?:\.\d{2})?)',  # ¥2881.16 格式
                r'[￥¥]\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
                r'(\d{1,4}(?:\.\d{2}))',  # 直接的数字格式如 2881.16
            ]

            for pattern in amount_patterns:
                amount_match = re.search(pattern, line)
                if amount_match:
                    amount_str = amount_match.group(1).replace(',', '')
                    try:
                        amount_value = float(amount_str)
                        # 调整金额范围，包含更小的金额
                        if 100 <= amount_value <= 10000000:
                            # 避免识别到税额或其他小金额，优先识别较大的金额
                            if not result['AmountInFiguers'] or amount_value > float(result['AmountInFiguers']):
                                result['AmountInFiguers'] = amount_str
                                print(f"✅ 识别小写金额: {line} -> {amount_str}")
                    except ValueError:
                        continue
        
        # 税额识别 (TotalTax) - 改进的识别逻辑
        if not result['TotalTax']:
            # 直接匹配¥符号后的金额，且金额较小（通常是税额）
            tax_match = re.search(r'¥(\d+(?:\.\d{2})?)', line)
            if tax_match:
                tax_amount = tax_match.group(1)
                try:
                    tax_value = float(tax_amount)
                    # 税额通常比主金额小，且在合理范围内
                    if 10 <= tax_value <= 50000:
                        if not result['TotalTax'] or tax_value < float(result.get('AmountInFiguers', '999999')):
                            result['TotalTax'] = tax_amount
                            print(f"✅ 识别税额: {line} -> {tax_amount}")
                except ValueError:
                    pass

        # 价税合计 (TotalAmount) - 改进的识别逻辑
        if not result['TotalAmount']:
            # 查找价税合计相关的行
            if '价税合计' in line or '合计' in line:
                print(f"找到价税合计相关行: {line}")
                # 在当前行或后续行查找金额
                lines_to_check = [line] + [all_text[j][0] for j in range(i+1, min(len(all_text), i+3))]
                for check_line in lines_to_check:
                    total_match = re.search(r'¥(\d+(?:\.\d{2})?)', check_line)
                    if total_match:
                        total_amount = total_match.group(1)
                        try:
                            total_value = float(total_amount)
                            # 价税合计通常是最大的金额
                            if total_value >= 1000:
                                result['TotalAmount'] = total_amount
                                print(f"✅ 识别价税合计: {check_line} -> {total_amount}")
                                break
                        except ValueError:
                            continue
        
        # 销售方名称识别 - 强化OCR错误修正
        if not result['SellerName']:
            # 查找销售方名称
            if '销' in line and '名称' in line:
                # 提取名称部分
                name_match = re.search(r'名称[：:]\s*(.+)', line)
                if name_match:
                    seller_name = name_match.group(1).strip()
                    # 应用OCR错误修正
                    seller_name = correct_company_name_ocr(seller_name)
                    result['SellerName'] = seller_name
                    print(f"✅ 识别销售方: {line} -> {seller_name}")
            
            # 如果还没找到销售方，查找包含公司名称的行
            elif ('公司' in line or '有限' in line) and len(line) > 5:
                # 排除购方信息
                if '购' not in line and '电网' not in line and '供电局' not in line:
                    # 常见的销售方公司名称模式
                    if any(keyword in line for keyword in ['科技', '新能源', '实业', '贸易', '工程', '建设']):
                        corrected_name = correct_company_name_ocr(line.strip())
                        result['SellerName'] = corrected_name
                        if corrected_name != line.strip():
                            print(f"✅ 识别销售方(OCR修正): {line} -> {corrected_name}")
                        else:
                            print(f"✅ 识别销售方: {line}")
        
        # 备注信息识别
        if not result['Remarks']:
            if '银行账号' in line or '开户银行' in line:
                result['Remarks'] = line.strip()
                print(f"✅ 识别备注: {line}")
    
    print(f"\n发票信息提取完成:")
    for key, value in result.items():
        print(f"  {key}: {value}")
    
    return result

def correct_company_name_ocr(text):
    """OCR错误修正 - 针对公司名称的常见错误"""
    corrections = {
        # 公司名称修正
        '奥捷': ['樊捷', '奧捷', '澳捷', '敖捷', '嗷捷'],
        '奥源': ['樊源', '奧源', '澳源', '敖源', '嗷源'],
        '来宁': ['来亍', '來宁', '来寧', '來寧', '来宇'],
        '锦阳': ['锦阴', '錦阳', '锦陽', '錦陽', '锦阴'],
        '顺洋': ['順洋', '顺羊', '順羊', '顺样'],
        '揭阳': ['揭阴', '揭陽', '揭陰', '揭样'],
        '广东': ['广氽', '廣东', '廣氽', '广束'],
        '天美': ['夭美', '天羙', '夭羙', '天关'],
        
        # 其他常见错误
        '新能源': ['新龙源', '新能原', '新龙原'],
        '科技': ['科拔', '科技', '科枝'],
        '有限公司': ['有限公司', '有限公可', '有限公司'],
    }
    
    corrected_text = text
    for correct, errors in corrections.items():
        for error in errors:
            if error in corrected_text:
                corrected_text = corrected_text.replace(error, correct)
                print(f"    OCR修正: '{error}' -> '{correct}'")
    
    return corrected_text

def recognize_invoice_surya(file_path):
    """使用Surya OCR识别发票信息"""
    print(f"开始处理发票: {file_path}")
    
    temp_image_path = None
    try:
        # 如果是PDF，转换为图片
        if file_path.lower().endswith('.pdf'):
            print("正在转换PDF为图片...")
            temp_image_path = pdf_to_image_pymupdf(file_path)
            
            if not temp_image_path:
                print("PDF转换失败")
                return None
            
            file_path = temp_image_path
            print("PDF转换完成")
        
        # 初始化Surya OCR处理器（单例模式）
        if not hasattr(recognize_invoice_surya, 'ocr_processor'):
            recognize_invoice_surya.ocr_processor = SuryaOCRProcessor()
        
        # 使用Surya OCR识别图片
        print("正在进行OCR识别...")
        result = recognize_invoice_surya.ocr_processor.recognize_image(file_path)
        
        if not result:
            print("OCR识别结果为空")
            return None
        
        print(f"OCR识别到 {len(result)} 个文本区域")
        
        # 从OCR结果中提取发票信息
        invoice_data = extract_invoice_info_surya(result)
        print(f"识别结果: {invoice_data}")
        return invoice_data
        
    except Exception as e:
        print(f"发票识别失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        if temp_image_path and os.path.exists(temp_image_path):
            try:
                os.remove(temp_image_path)
                print("临时文件已清理")
            except:
                pass

class InvoiceProcessWindow:
    """完整的发票处理窗口 - 集成Surya OCR"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("发票处理配置 - Surya高精度OCR版本")
        self.root.geometry("800x700")  # 增加窗口高度以适应日志窗口

        # 文件路径变量
        self.template_path = tk.StringVar(value=r"D:\python\pythonpj\gf\1\模板.xls")
        self.export_list_path = tk.StringVar(value=r"D:\python\pythonpj\gf\1\202501导出清单.xlsx")
        self.invoice_folder = tk.StringVar()
        # 关键词变量
        self.keyword = tk.StringVar(value="锦阳-顺洋-来宁-奥捷-奥源")

        # 添加日志文本框
        self.log_text = None

        self.create_widgets()

    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_label = tk.Label(self.root, text="发票处理程序 - Surya高精度OCR版本",
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)

        # 说明文本
        info_text = """
基于GitHub 17.7k星标的Surya OCR项目，支持90+语言的高精度文档OCR识别
完整集成原有数据处理流程，提供准确的发票信息提取和Excel报表生成
        """
        info_label = tk.Label(self.root, text=info_text, font=("Arial", 10), fg="blue")
        info_label.pack(pady=5)

        # 创建主框架
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # 模板文件选择
        template_frame = tk.Frame(main_frame)
        template_frame.pack(fill=tk.X, pady=5)
        tk.Label(template_frame, text="模板文件:", font=("Arial", 12)).pack(anchor=tk.W)
        template_entry_frame = tk.Frame(template_frame)
        template_entry_frame.pack(fill=tk.X, pady=2)
        tk.Entry(template_entry_frame, textvariable=self.template_path, font=("Arial", 10)).pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Button(template_entry_frame, text="浏览", command=self.select_template).pack(side=tk.RIGHT, padx=(5, 0))

        # 导出清单选择
        export_frame = tk.Frame(main_frame)
        export_frame.pack(fill=tk.X, pady=5)
        tk.Label(export_frame, text="导出清单:", font=("Arial", 12)).pack(anchor=tk.W)
        export_entry_frame = tk.Frame(export_frame)
        export_entry_frame.pack(fill=tk.X, pady=2)
        tk.Entry(export_entry_frame, textvariable=self.export_list_path, font=("Arial", 10)).pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Button(export_entry_frame, text="浏览", command=self.select_export_list).pack(side=tk.RIGHT, padx=(5, 0))

        # 发票文件夹选择
        folder_frame = tk.Frame(main_frame)
        folder_frame.pack(fill=tk.X, pady=5)
        tk.Label(folder_frame, text="发票文件夹:", font=("Arial", 12)).pack(anchor=tk.W)
        folder_entry_frame = tk.Frame(folder_frame)
        folder_entry_frame.pack(fill=tk.X, pady=2)
        tk.Entry(folder_entry_frame, textvariable=self.invoice_folder, font=("Arial", 10)).pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Button(folder_entry_frame, text="浏览", command=self.select_invoice_folder).pack(side=tk.RIGHT, padx=(5, 0))

        # 关键词设置
        keyword_frame = tk.Frame(main_frame)
        keyword_frame.pack(fill=tk.X, pady=5)
        tk.Label(keyword_frame, text="关键词 (用-分隔):", font=("Arial", 12)).pack(anchor=tk.W)
        tk.Entry(keyword_frame, textvariable=self.keyword, font=("Arial", 10)).pack(fill=tk.X, pady=2)

        # 处理按钮
        process_button = tk.Button(main_frame, text="开始处理", command=self.process_invoices,
                                 font=("Arial", 14, "bold"), bg="green", fg="white", height=2)
        process_button.pack(pady=15)

        # 日志显示
        log_frame = tk.Frame(main_frame)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        tk.Label(log_frame, text="处理日志:", font=("Arial", 12)).pack(anchor=tk.W)

        # 创建文本框和滚动条
        text_frame = tk.Frame(log_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(text_frame, font=("Consolas", 9))
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def select_template(self):
        """选择模板文件"""
        file_path = filedialog.askopenfilename(
            title="选择模板文件",
            filetypes=[("Excel files", "*.xls *.xlsx"), ("All files", "*.*")]
        )
        if file_path:
            self.template_path.set(file_path)

    def select_export_list(self):
        """选择导出清单文件"""
        file_path = filedialog.askopenfilename(
            title="选择导出清单文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if file_path:
            self.export_list_path.set(file_path)

    def select_invoice_folder(self):
        """选择发票文件夹"""
        folder = filedialog.askdirectory(title="选择发票文件夹")
        if folder:
            self.invoice_folder.set(folder)

    def log(self, message):
        """添加日志消息"""
        if self.log_text:
            self.log_text.insert(tk.END, message + "\n")
            self.log_text.see(tk.END)
            self.root.update()

    def find_matching_invoice_for_keyword(self, keyword):
        """为指定关键词查找匹配的发票"""
        folder_path = self.invoice_folder.get()

        if not folder_path or not keyword:
            messagebox.showerror("错误", "请选择发票文件夹并输入关键词")
            return None, None

        # 遍历文件夹中的所有PDF文件
        for file in os.listdir(folder_path):
            if file.lower().endswith('.pdf'):
                pdf_path = os.path.join(folder_path, file)
                try:
                    # 使用Surya OCR识别发票
                    invoice_data = recognize_invoice_surya(pdf_path)
                    if invoice_data:
                        # 获取销售方名称
                        seller_name = invoice_data.get('SellerName', '')
                        # 检查关键词是否在销售方名称中
                        if keyword in seller_name:
                            self.log(f"找到匹配发票: {file}")
                            return pdf_path, invoice_data
                except Exception as e:
                    self.log(f"处理发票 {file} 时出错: {str(e)}")
                    continue

        return None, None

    def process_invoices(self):
        """处理发票的主要逻辑"""
        try:
            # 验证输入
            if not self.template_path.get():
                messagebox.showerror("错误", "请选择模板文件")
                return

            if not self.export_list_path.get():
                messagebox.showerror("错误", "请选择导出清单文件")
                return

            if not self.invoice_folder.get():
                messagebox.showerror("错误", "请选择发票文件夹")
                return

            if not self.keyword.get():
                messagebox.showerror("错误", "请输入关键词")
                return

            # 解析关键词
            keywords = [kw.strip() for kw in self.keyword.get().split('-') if kw.strip()]

            self.log("="*60)
            self.log("开始处理发票...")
            self.log(f"模板文件: {self.template_path.get()}")
            self.log(f"导出清单: {self.export_list_path.get()}")
            self.log(f"发票文件夹: {self.invoice_folder.get()}")
            self.log(f"关键词: {keywords}")
            self.log("="*60)

            # 为每个关键词查找匹配的发票
            pdf_paths_data = {}
            for keyword in keywords:
                self.log(f"\n查找关键词 '{keyword}' 的匹配发票...")
                pdf_path, invoice_data = self.find_matching_invoice_for_keyword(keyword)
                if pdf_path and invoice_data:
                    pdf_paths_data[keyword] = (pdf_path, invoice_data)
                    self.log(f"✅ 找到匹配发票: {os.path.basename(pdf_path)}")
                else:
                    self.log(f"❌ 未找到关键词 '{keyword}' 的匹配发票")

            if not pdf_paths_data:
                self.log("❌ 没有找到任何匹配的发票")
                messagebox.showwarning("警告", "没有找到匹配的发票")
                return

            self.log(f"\n总共找到 {len(pdf_paths_data)} 个匹配的发票")

            # 开始处理Excel文件
            self.log("\n开始处理Excel文件...")

            # 启动Excel应用
            excel = win32com.client.Dispatch("Excel.Application")
            excel.Visible = False
            excel.DisplayAlerts = False

            try:
                # 打开模板文件
                template_path = self.template_path.get()
                export_list_path = self.export_list_path.get()

                self.log(f"正在打开模板文件...")
                wb = excel.Workbooks.Open(template_path)
                ws = wb.Worksheets(1)

                # 获取当前行数
                current_row = ws.UsedRange.Rows.Count + 1
                self.log(f"模板当前行数: {current_row-1}, 新数据从第{current_row}行开始")

                # 对每个关键词进行处理
                for keyword, (pdf_path, invoice_data) in pdf_paths_data.items():
                    self.log(f"\n{'='*50}")
                    self.log(f"开始处理关键词: {keyword}")
                    self.log(f"使用发票文件: {os.path.basename(pdf_path)}")

                    # 读取导出清单
                    self.log(f"正在读取导出清单...")
                    export_wb = excel.Workbooks.Open(export_list_path)
                    export_ws = export_wb.Worksheets(1)

                    # 获取数据范围
                    used_range = export_ws.UsedRange
                    nrows = used_range.Rows.Count
                    self.log(f"导出清单总行数: {nrows}")

                    # 收集匹配的行
                    self.log(f"正在查找匹配的行...")
                    valid_rows = []
                    for row in range(2, nrows + 1):
                        cell_value = export_ws.Cells(row, 2).Value
                        amount_value = export_ws.Cells(row, 9).Value

                        if (cell_value and keyword in str(cell_value) and
                            amount_value and float(amount_value or 0) != 0):
                            valid_rows.append(row)
                            self.log(f"找到匹配行: 第{row}行, 内容: {cell_value}, 金额: {amount_value}")

                    if not valid_rows:
                        self.log(f"❌ 关键词 '{keyword}' 在导出清单中没有找到匹配的行")
                        export_wb.Close()
                        continue

                    self.log(f"✅ 找到 {len(valid_rows)} 个匹配行")
                    valid_row_count = len(valid_rows)

                    # 复制数据到模板
                    self.log(f"正在复制数据到模板...")
                    for i, row in enumerate(valid_rows):
                        target_row = current_row + i

                        # 复制A到I列的数据
                        for col in range(1, 10):
                            source_value = export_ws.Cells(row, col).Value
                            ws.Cells(target_row, col).Value = source_value

                    # 关闭导出清单
                    export_wb.Close()

                    # 处理发票数据
                    if invoice_data:
                        self.log("\n处理发票数据:")
                        self.log(f"发票类型: {invoice_data.get('InvoiceTypeOrg', '未知')}")
                        self.log(f"小写金额: {invoice_data.get('AmountInFiguers', '未知')}")
                        self.log(f"价税合计: {invoice_data.get('TotalAmount', '未知')}")
                        self.log(f"税率: {invoice_data.get('CommodityTaxRate', '未知')}")
                        self.log(f"税额: {invoice_data.get('TotalTax', '未知')}")
                        self.log(f"销售方: {invoice_data.get('SellerName', '未知')}")

                        # 提取并填充发票类型 (J列)
                        invoice_type = invoice_data.get('InvoiceTypeOrg', '')
                        if '增值税专用发票' in invoice_type:
                            ws.Range(f"J{current_row}").Value = '增值税专用发票'
                        elif '增值税普通发票' in invoice_type:
                            ws.Range(f"J{current_row}").Value = '增值税普通发票'

                        # 提取并填充小写金额 (K列)
                        amount = invoice_data.get('AmountInFiguers', '')
                        if amount:
                            ws.Range(f"K{current_row}").Value = float(amount)

                        # 提取并填充价税合计 (L列)
                        total_amount = invoice_data.get('TotalAmount', '')
                        if total_amount:
                            ws.Range(f"L{current_row}").Value = float(total_amount)

                        # 提取并填充税率 (M列)
                        tax_rate = invoice_data.get('CommodityTaxRate', '')
                        if tax_rate:
                            if isinstance(tax_rate, list):
                                tax_rate = tax_rate[0] if tax_rate else ''
                            ws.Range(f"M{current_row}").Value = tax_rate

                        # 提取并填充税额 (N列)
                        tax_amount = invoice_data.get('TotalTax', '')
                        if tax_amount:
                            ws.Range(f"N{current_row}").Value = float(tax_amount)

                    # 合并J到N列的单元格
                    for col_letter in ['J', 'K', 'L', 'M', 'N']:
                        range_to_merge = ws.Range(f"{col_letter}{current_row}:{col_letter}{current_row+valid_row_count-1}")
                        range_to_merge.Merge()

                    # 计算当前关键词的P和Q列公式
                    p_range = ws.Range(f"P{current_row}:P{current_row+valid_row_count-1}")
                    p_range.Merge()
                    p_range.Formula = f"=L{current_row}-SUM(G{current_row}:G{current_row+valid_row_count-1})"

                    q_range = ws.Range(f"Q{current_row}:Q{current_row+valid_row_count-1}")
                    q_range.Merge()
                    q_range.Formula = f"=N{current_row}-SUM(H{current_row}:H{current_row+valid_row_count-1})"

                    # 计算O列的验证
                    i_sum = sum(ws.Cells(row, 9).Value or 0 for row in range(current_row, current_row+valid_row_count))
                    k_value = ws.Range(f"K{current_row}").Value or 0

                    o_range = ws.Range(f"O{current_row}:O{current_row+valid_row_count-1}")
                    o_range.Merge()
                    if abs(i_sum - float(k_value)) < 0.01:
                        o_range.Value = "是"
                        self.log(f"✅ 金额验证通过: 导出清单合计={i_sum}, 发票金额={k_value}")
                    else:
                        o_range.Value = "否"
                        self.log(f"❌ 金额验证失败: 导出清单合计={i_sum}, 发票金额={k_value}")

                    # 更新当前行位置
                    current_row += valid_row_count
                    self.log(f"✅ 关键词 '{keyword}' 处理完成")

                # 保存文件
                output_path = template_path.replace('.xls', '_处理结果.xlsx')
                self.log(f"\n正在保存结果文件: {output_path}")
                wb.SaveAs(output_path, FileFormat=51)  # xlsx格式
                wb.Close()

                self.log(f"✅ 处理完成！结果已保存到: {output_path}")
                messagebox.showinfo("完成", f"处理完成！\n结果已保存到:\n{output_path}")

            except Exception as e:
                self.log(f"❌ 处理过程中发生错误: {str(e)}")
                messagebox.showerror("错误", f"处理过程中发生错误:\n{str(e)}")
                import traceback
                traceback.print_exc()
            finally:
                try:
                    excel.Quit()
                except:
                    pass

        except Exception as e:
            self.log(f"❌ 发生错误: {str(e)}")
            messagebox.showerror("错误", f"发生错误:\n{str(e)}")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    print("发票处理程序 - 完整集成Surya高精度OCR版本")
    print("基于GitHub 17.7k星标的Surya OCR项目")
    print("支持90+语言，特别优化中文发票识别")
    print("完整集成原有数据处理流程")
    print("="*60)

    # 启动GUI
    app = InvoiceProcessWindow()
    app.run()

if __name__ == "__main__":
    main()
