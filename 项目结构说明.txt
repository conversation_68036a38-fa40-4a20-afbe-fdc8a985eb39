发票处理程序 - 项目结构说明
=====================================

项目根目录：D:\vscode project\发票处理

主要文件：
├── fppl-21.尝试替代百度识别.py    # 主程序文件（已更新为EasyOCR版本）
├── 启动发票处理.bat              # 启动脚本（推荐使用）
├── 测试EasyOCR.py               # EasyOCR测试脚本
├── requirements.txt             # 依赖包列表
├── README.md                   # 使用说明文档
├── 项目结构说明.txt             # 本文件
└── venv/                       # 虚拟环境目录
    ├── Scripts/                # 虚拟环境脚本
    ├── Lib/                    # 已安装的包
    └── ...

工作目录：
└── 1/                          # 工作文件目录
    ├── 模板.xls                # Excel模板文件
    ├── 202501导出清单.xlsx      # 导出清单文件
    ├── *.pdf                   # 发票PDF文件
    └── *.xlsx                  # 生成的报销表文件

主要改进：
==========

1. OCR引擎替换：
   - 原来：百度API（需要网络）
   - 现在：EasyOCR（本地处理）

2. 环境配置：
   - 创建了独立的虚拟环境
   - 安装了所有必要的依赖包
   - 提供了启动脚本

3. 功能增强：
   - 改进了发票信息提取算法
   - 增加了更多的文本匹配模式
   - 提高了识别准确率

4. 用户体验：
   - 添加了详细的日志输出
   - 提供了测试脚本
   - 创建了使用说明文档

技术栈：
========
- Python 3.13
- EasyOCR 1.7.2（中英文OCR识别）
- PyTorch 2.7.1（深度学习框架）
- OpenCV 4.11.0（图像处理）
- pdf2image 1.17.0（PDF转图片）
- pywin32 310（Windows COM接口）

使用流程：
==========
1. 双击"启动发票处理.bat"启动程序
2. 在GUI界面中配置文件路径和关键词
3. 选择发票文件夹
4. 点击"开始处理"
5. 程序自动识别发票并生成报销表

注意事项：
==========
1. 首次运行会下载OCR模型（约200MB）
2. 使用CPU处理，速度相对较慢
3. 建议使用高质量的发票图片/PDF
4. 处理完成后请检查识别结果的准确性
