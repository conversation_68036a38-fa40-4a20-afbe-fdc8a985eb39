import win32com.client
from datetime import datetime
import os
import warnings
import base64
import urllib
import requests
import tkinter as tk
from tkinter import filedialog, ttk
from tkinter import messagebox
import easyocr
import re

# 尝试导入PDF处理库
try:
    import fitz  # PyMuPDF
    PDF_PROCESSOR = "pymupdf"
    print("使用PyMuPDF处理PDF文件")
except ImportError:
    try:
        from pdf2image import convert_from_path
        PDF_PROCESSOR = "pdf2image"
        # 配置Poppler路径（用于PDF转图片）
        POPPLER_PATH = r'D:\python\源码\证件识别\poppler-24.02.0\Library\bin'
        print("使用pdf2image处理PDF文件")
    except ImportError:
        PDF_PROCESSOR = None
        print("警告：无法导入PDF处理库，将无法处理PDF文件")

# 初始化EasyOCR，使用中英文识别
print("正在初始化EasyOCR...")
ocr = easyocr.Reader(['ch_sim', 'en'], gpu=False)  # 中文简体和英文，使用CPU
print("EasyOCR初始化完成！")

def extract_invoice_info(text_list):
    """从EasyOCR结果中提取发票信息
    EasyOCR返回格式: [(bbox, text, confidence), ...]
    """
    result = {
        'InvoiceTypeOrg': '',
        'AmountInFiguers': '',
        'TotalAmount': '',
        'CommodityTaxRate': '',
        'TotalTax': '',
        'SellerName': '',
        'Remarks': ''
    }

    # 将所有识别的文本保存，包括低置信度的（因为有些关键信息可能置信度较低）
    all_text = []
    for bbox, text, confidence in text_list:
        all_text.append((text.strip(), confidence))

    print("开始提取发票信息...")

    # 遍历所有文本进行信息提取
    for i, (line, confidence) in enumerate(all_text):
        line = str(line).strip()

        # 发票类型 - 处理OCR识别错误
        if ('增值税' in line or '僧值税' in line or '电子发票' in line) and not result['InvoiceTypeOrg']:
            if '专用' in line:
                result['InvoiceTypeOrg'] = '增值税专用发票'
            elif '普通' in line or '电子发票' in line:
                result['InvoiceTypeOrg'] = '增值税普通发票'
            print(f"识别发票类型: {line} -> {result['InvoiceTypeOrg']}")

        # 价税合计 - 查找小写金额
        if ('小写' in line or '羊' in line or '￥' in line) and not result['TotalAmount']:
            # 处理OCR错误：羊 -> ￥
            line_fixed = line.replace('羊', '￥').replace('芏', '￥')
            total_patterns = [
                r'小写.*?￥?(\d+\.?\d*)',
                r'￥(\d+\.?\d*)',
                r'(\d+\.\d{2})'  # 直接匹配金额格式
            ]
            for pattern in total_patterns:
                total_match = re.search(pattern, line_fixed)
                if total_match:
                    total_str = total_match.group(1)
                    if '.' in total_str and len(total_str.split('.')[1]) <= 2:
                        result['TotalAmount'] = total_str
                        result['AmountInFiguers'] = total_str  # 小写金额同时作为金额
                        print(f"识别价税合计: {line} -> {total_str}")
                        break

        # 税额 - 查找税额数字
        if not result['TotalTax']:
            # 查找可能的税额（通常是较大的数字，小于总金额）
            tax_patterns = [
                r'芏(\d+\.?\d*)',  # OCR错误：芏 -> ￥
                r'税.*?(\d+\.?\d*)',
                r'^(\d{4,}\.\d{2})$'  # 独立的金额数字
            ]
            for pattern in tax_patterns:
                tax_match = re.search(pattern, line)
                if tax_match:
                    tax_str = tax_match.group(1)
                    # 验证是否为合理的税额（通常小于总金额的50%）
                    try:
                        tax_amount = float(tax_str)
                        if result['TotalAmount']:
                            total_amount = float(result['TotalAmount'])
                            if 0 < tax_amount < total_amount * 0.5:
                                result['TotalTax'] = tax_str
                                print(f"识别税额: {line} -> {tax_str}")
                                break
                        elif 1000 < tax_amount < 100000:  # 合理的税额范围
                            result['TotalTax'] = tax_str
                            print(f"识别税额: {line} -> {tax_str}")
                            break
                    except:
                        pass

        # 税率 - 查找税率信息
        if not result['CommodityTaxRate']:
            # 处理常见的税率格式
            tax_rate_patterns = [
                r'(\d+)%',
                r'^(\d{1,2})$'  # 单独的数字，可能是税率
            ]
            for pattern in tax_rate_patterns:
                tax_rate_match = re.search(pattern, line)
                if tax_rate_match:
                    rate_num = tax_rate_match.group(1)
                    # 验证是否为合理的税率
                    try:
                        rate_val = int(rate_num)
                        if rate_val in [0, 3, 6, 9, 13, 16, 17]:  # 常见税率
                            result['CommodityTaxRate'] = [rate_num + '%']
                            print(f"识别税率: {line} -> {rate_num}%")
                            break
                        elif rate_val == 135:  # OCR错误：135 -> 13%
                            result['CommodityTaxRate'] = ['13%']
                            print(f"识别税率: {line} -> 13% (修正135)")
                            break
                    except:
                        pass

            # 如果还没找到税率，根据税额和总金额计算
            if not result['CommodityTaxRate'] and result['TotalTax'] and result['TotalAmount']:
                try:
                    tax_amount = float(result['TotalTax'])
                    total_amount = float(result['TotalAmount'])
                    net_amount = total_amount - tax_amount
                    if net_amount > 0:
                        calculated_rate = (tax_amount / net_amount) * 100
                        # 匹配到最接近的标准税率
                        standard_rates = [0, 3, 6, 9, 13, 16, 17]
                        closest_rate = min(standard_rates, key=lambda x: abs(x - calculated_rate))
                        if abs(closest_rate - calculated_rate) < 2:  # 误差在2%以内
                            result['CommodityTaxRate'] = [f'{closest_rate}%']
                            print(f"计算税率: {calculated_rate:.1f}% -> {closest_rate}%")
                except:
                    pass

        # 销售方名称 - 查找销售方
        if '销' in line and '名称' in line and not result['SellerName']:
            # 查找下一行或当前行的公司名称
            seller_patterns = [
                r'名称[:：]\s*(.+)',
                r'销.*?名称.*?[:：]\s*(.+)'
            ]
            for pattern in seller_patterns:
                seller_match = re.search(pattern, line)
                if seller_match:
                    seller_name = seller_match.group(1).strip()
                    if len(seller_name) > 3:
                        result['SellerName'] = seller_name
                        print(f"识别销售方: {line} -> {seller_name}")
                        break

            # 如果当前行没找到，查找下一行
            if not result['SellerName'] and i + 1 < len(all_text):
                next_line = all_text[i + 1][0]
                if '公司' in next_line or '有限' in next_line:
                    result['SellerName'] = next_line.strip()
                    print(f"识别销售方(下一行): {next_line}")

        # 如果还没找到销售方，查找包含公司名称的行
        if not result['SellerName'] and ('公司' in line or '有限' in line) and len(line) > 5:
            # 排除购方信息
            if '购' not in line and '电网' not in line and '供电局' not in line:
                # 常见的销售方公司名称模式
                if any(keyword in line for keyword in ['科技', '新能源', '实业', '贸易', '工程', '建设']):
                    result['SellerName'] = line.strip()
                    print(f"识别销售方(公司名): {line}")

        # 备注信息（开户行和账号）
        if '开户行' in line or '账号' in line or '银行' in line:
            result['Remarks'] = result['Remarks'] + line + ';'

    print(f"信息提取完成: {result}")
    return result

def pdf_to_image_pymupdf(pdf_path):
    """使用PyMuPDF将PDF转换为图片"""
    try:
        import fitz
        doc = fitz.open(pdf_path)
        page = doc[0]  # 获取第一页

        # 设置缩放比例以提高图片质量
        mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
        pix = page.get_pixmap(matrix=mat)

        temp_image_path = "temp_invoice_pymupdf.png"
        pix.save(temp_image_path)
        doc.close()

        print(f"PyMuPDF转换完成: {temp_image_path}")
        return temp_image_path
    except Exception as e:
        print(f"PyMuPDF转换失败: {str(e)}")
        return None

def pdf_to_image_pdf2image(pdf_path):
    """使用pdf2image将PDF转换为图片"""
    try:
        from pdf2image import convert_from_path
        pages = convert_from_path(pdf_path, poppler_path=POPPLER_PATH)
        temp_image_path = "temp_invoice_pdf2image.png"
        pages[0].save(temp_image_path, 'PNG')
        print(f"pdf2image转换完成: {temp_image_path}")
        return temp_image_path
    except Exception as e:
        print(f"pdf2image转换失败: {str(e)}")
        return None

def recognize_invoice(file_path):
    """使用EasyOCR识别发票信息"""
    if not os.path.exists(file_path):
        print(f"错误：文件不存在 - {file_path}")
        return None

    print(f"开始处理发票: {file_path}")

    temp_image_path = None
    try:
        # 如果是PDF，转换为图片
        if file_path.lower().endswith('.pdf'):
            print("正在转换PDF为图片...")

            if PDF_PROCESSOR == "pymupdf":
                temp_image_path = pdf_to_image_pymupdf(file_path)
            elif PDF_PROCESSOR == "pdf2image":
                temp_image_path = pdf_to_image_pdf2image(file_path)
            else:
                print("错误：没有可用的PDF处理库")
                return None

            if not temp_image_path:
                print("PDF转换失败")
                return None

            file_path = temp_image_path
            print("PDF转换完成")

        # 使用EasyOCR识别图片
        print("正在进行OCR识别...")
        result = ocr.readtext(file_path)

        if not result:
            print("OCR识别结果为空")
            return None

        print(f"OCR识别到 {len(result)} 个文本区域")

        # 从OCR结果中提取发票信息
        invoice_data = extract_invoice_info(result)
        print(f"识别结果: {invoice_data}")
        return invoice_data

    except Exception as e:
        print(f"发票识别失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        if temp_image_path and os.path.exists(temp_image_path):
            try:
                os.remove(temp_image_path)
                print("临时文件已清理")
            except:
                pass

class InvoiceProcessWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("发票处理配置")
        self.root.geometry("800x600")  # 增加窗口高度以适应日志窗口
        
        # 文件路径变量
        self.template_path = tk.StringVar(value=r"D:\python\pythonpj\gf\1\模板.xls")
        self.export_list_path = tk.StringVar(value=r"D:\python\pythonpj\gf\1\202501导出清单.xlsx")
        self.invoice_folder = tk.StringVar()
        # 关键词变量
        self.keyword = tk.StringVar(value="锦阳-顺洋-来宁-奥捷-奥源")
        
        # 添加日志文本框
        self.log_text = None
        
        self.create_widgets()
        
    def create_widgets(self):
        # 模板文件选择
        template_frame = ttk.Frame(self.root, padding="10")
        template_frame.pack(fill="x")
        ttk.Label(template_frame, text="模板文件:").pack(side="left")
        ttk.Entry(template_frame, textvariable=self.template_path, width=60).pack(side="left", padx=5)
        ttk.Button(template_frame, text="浏览", 
                  command=lambda: self.browse_file(self.template_path, [("Excel files", "*.xls;*.xlsx")])).pack(side="left")
        
        # 导出清单文件选择
        export_frame = ttk.Frame(self.root, padding="10")
        export_frame.pack(fill="x")
        ttk.Label(export_frame, text="导出清单:").pack(side="left")
        ttk.Entry(export_frame, textvariable=self.export_list_path, width=60).pack(side="left", padx=5)
        ttk.Button(export_frame, text="浏览", 
                  command=lambda: self.browse_file(self.export_list_path, [("Excel files", "*.xls;*.xlsx")])).pack(side="left")
        
        # 原有的控件
        keyword_frame = ttk.Frame(self.root, padding="10")
        keyword_frame.pack(fill="x")
        ttk.Label(keyword_frame, text="关键词(用-分隔):").pack(side="left")
        ttk.Entry(keyword_frame, textvariable=self.keyword, width=60).pack(side="left", padx=5)
        
        folder_frame = ttk.Frame(self.root, padding="10")
        folder_frame.pack(fill="x")
        ttk.Label(folder_frame, text="发票文件夹:").pack(side="left")
        ttk.Entry(folder_frame, textvariable=self.invoice_folder, width=60).pack(side="left", padx=5)
        ttk.Button(folder_frame, text="浏览", command=self.browse_folder).pack(side="left")
        
        # 处理按钮
        ttk.Button(self.root, text="开始处理", command=self.start_process).pack(pady=10)
        
        # 添加日志窗口
        log_frame = ttk.LabelFrame(self.root, text="处理日志", padding="10")
        log_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 创建文本框和滚动条
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        # 放置文本框和滚动条
        self.log_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 设置文本框只读
        self.log_text.configure(state='disabled')
    
    def browse_file(self, string_var, file_types):
        filename = filedialog.askopenfilename(filetypes=file_types)
        if filename:
            string_var.set(filename)
    
    def browse_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.invoice_folder.set(folder)
            
    def find_matching_invoice_for_keyword(self, keyword):
        folder_path = self.invoice_folder.get()
        
        if not folder_path or not keyword:
            messagebox.showerror("错误", "请选择发票文件夹并输入关键词")
            return None, None
            
        # 遍历文件夹中的所有PDF文件
        for file in os.listdir(folder_path):
            if file.lower().endswith('.pdf'):
                pdf_path = os.path.join(folder_path, file)
                try:
                    # 识别发票
                    invoice_data = recognize_invoice(pdf_path)
                    if invoice_data:
                        # 获取销售方名称
                        seller_name = invoice_data.get('SellerName', '')
                        # 检查关键词是否在销售方名称中
                        if keyword in seller_name:
                            print(f"找到匹配发票: {file}")
                            return pdf_path, invoice_data
                except Exception as e:
                    print(f"处理发票 {file} 时出错: {str(e)}")
                    continue
        
        return None, None
            
    def log_message(self, message):
        """向日志窗口添加消息"""
        self.log_text.configure(state='normal')
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)  # 滚动到最新内容
        self.log_text.configure(state='disabled')
        # 更新GUI
        self.root.update()
    
    def start_process(self):
        try:
            # 清空日志窗口
            self.log_text.configure(state='normal')
            self.log_text.delete(1.0, tk.END)
            self.log_text.configure(state='disabled')
            
            # 验证文件路径
            if not all(os.path.exists(path) for path in [self.template_path.get(), self.export_list_path.get()]):
                self.log_message("错误: 请确保模板文件和导出清单文件路径正确")
                messagebox.showerror("错误", "请确保模板文件和导出清单文件路径正确")
                return
            
            # 获取所有关键词
            keywords = [k.strip() for k in self.keyword.get().split('-')]
            self.log_message(f"开始处理关键词: {', '.join(keywords)}")
            
            pdf_paths_data = {}
            
            # 先收集所有关键词对应的发票数据
            for keyword in keywords:
                self.log_message(f"\n查找关键词: {keyword}")
                matching_pdf, invoice_data = self.find_matching_invoice_for_keyword(keyword)
                if matching_pdf:
                    self.log_message(f"找到匹配发票: {os.path.basename(matching_pdf)}")
                    pdf_paths_data[keyword] = (matching_pdf, invoice_data)
                else:
                    self.log_message(f"未找到关键词 '{keyword}' 匹配的发票")
            
            if pdf_paths_data:
                # 传递文件路径到process_excel函数
                self.log_message("\n开始处理Excel文件...")
                process_excel(pdf_paths_data, self.template_path.get(), self.export_list_path.get(), 
                           log_callback=self.log_message)  # 传递日志回调函数
                self.log_message("\n处理完成!")
                messagebox.showinfo("成功", "所有关键词处理完成")
            else:
                self.log_message("\n错误: 没有找到任何匹配的发票")
                messagebox.showerror("错误", "没有找到任何匹配的发票")
            
        except Exception as e:
            error_message = f"处理过程中出错：{str(e)}"
            self.log_message(f"\n{error_message}")
            messagebox.showerror("错误", error_message)
        
    def run(self):
        self.root.mainloop()

def process_excel(pdf_paths_data=None, template_path=None, export_list_path=None, log_callback=None):
    def log(*args):
        """输出日志"""
        message = ' '.join(str(arg) for arg in args)  # 合并所有参数
        print(message)  # 控制台输出
        if log_callback:
            log_callback(message)  # GUI日志窗口输出
    
    # 使用传入的文件路径
    template_path = template_path or r"D:\python\pythonpj\gf\1\模板.xls"
    export_list_path = export_list_path or r"D:\python\pythonpj\gf\1\202501导出清单.xlsx"
    
    log(f"使用模板文件: {template_path}")
    log(f"使用导出清单: {export_list_path}")
    
    # 1. 读取文件
    template_path = os.path.abspath(template_path)
    export_list_path = os.path.abspath(export_list_path)
    
    # 修改新文件名，确保使用.xlsx后缀
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    keywords_str = "_".join(pdf_paths_data.keys())
    new_template_name = f"{keywords_str}报销表_{timestamp}.xlsx"  # 明确指定.xlsx后缀
    new_template_path = os.path.join(os.path.dirname(template_path), new_template_name)
    
    excel = win32com.client.Dispatch('Excel.Application')
    excel.Visible = False
    excel.DisplayAlerts = False
    
    try:
        # 添加更多日志输出
        log(f"开始处理Excel文件...")
        log(f"模板文件路径: {template_path}")
        log(f"导出清单路径: {export_list_path}")
        log(f"新文件保存路径: {new_template_path}")
        
        # 打开源文件并另存为新文件
        log("正在打开模板文件...")
        wb_template = excel.Workbooks.Open(template_path)
        
        # 修改保存部分，明确指定xlsx格式
        log("正在创建新文件...")
        wb_template.SaveAs(
            Filename=new_template_path,
            FileFormat=51,  # 51 = xlsx格式
            CreateBackup=False
        )
        wb_template.Close()
        log(f"已创建新文件: {new_template_name}")
        
        # 打开新文件进行编辑
        wb = excel.Workbooks.Open(new_template_path)
        ws = wb.Worksheets(1)
        
        # 记录当前插入位置
        current_row = 5
        sequence_number = 1  # 序号从1开始，全局计数
        
        # 对每个关键词进行处理
        for keyword, (pdf_path, invoice_data) in pdf_paths_data.items():
            log(f"\n{'='*50}")
            log(f"开始处理关键词: {keyword}")
            log(f"使用发票文件: {pdf_path}")
            
            # 读取导出清单时添加日志
            log(f"正在读取导出清单...")
            export_wb = excel.Workbooks.Open(export_list_path)
            export_ws = export_wb.Worksheets(1)
            
            # 获取数据范围
            used_range = export_ws.UsedRange
            nrows = used_range.Rows.Count
            log(f"导出清单总行数: {nrows}")
            
            # 收集匹配的行时添加更多信息
            log(f"正在查找匹配的行...")
            valid_rows = []
            for row in range(2, nrows + 1):
                cell_value = export_ws.Cells(row, 2).Value
                amount_value = export_ws.Cells(row, 9).Value
                
                if (cell_value and keyword in str(cell_value) and 
                    amount_value and float(amount_value or 0) != 0):
                    valid_rows.append(row)
                    log(f"找到匹配行: 第{row}行, 内容: {cell_value}, 金额: {amount_value}")
            
            valid_row_count = len(valid_rows)
            log(f"找到{valid_row_count}个匹配行")
            
            if valid_row_count == 0:
                log(f"关键词 {keyword} 没有找到有效行，跳过")
                export_wb.Close(False)
                continue
            
            # 在当前位置插入所需的行数
            if valid_row_count > 0:
                ws.Range(f"{current_row}:{current_row+valid_row_count-1}").EntireRow.Insert()
                
                # 设置插入行的格式，修改范围到U列
                insert_range = ws.Range(f"A{current_row}:U{current_row+valid_row_count-1}")
                insert_range.Borders.LineStyle = 1
                
                for border_id in range(7, 13):
                    insert_range.Borders(border_id).LineStyle = 1
                    insert_range.Borders(border_id).Weight = 2
                
                ws.Range(f"{current_row}:{current_row+valid_row_count-1}").RowHeight = 28.35
            
            # 填充数据
            for dest_idx, source_row in enumerate(valid_rows):
                dest_row = current_row + dest_idx
                
                # 填充序号（B列）使用全局计数
                ws.Cells(dest_row, 2).Value = sequence_number
                sequence_number += 1
                
                # 输出当前填充信息
                log(f"\n当前处理: ")
                log(f"目标行号(dest_row): {dest_row}")
                log(f"源数据行号(source_row): {source_row}")
                
                # 复制数据
                # A列 -> D列
                export_ws.Cells(source_row, 1).Copy()
                ws.Cells(dest_row, 4).PasteSpecial(Paste=-4163)
                excel.CutCopyMode = False
                
                # 其他列的填充
                ws.Cells(dest_row, 5).Value = export_ws.Cells(source_row, 2).Value  # B -> E
                ws.Cells(dest_row, 3).Value = export_ws.Cells(source_row, 19).Value # S -> C
                ws.Cells(dest_row, 6).Value = export_ws.Cells(source_row, 5).Value  # E -> F
                ws.Cells(dest_row, 7).Value = export_ws.Cells(source_row, 12).Value # L -> G
                ws.Cells(dest_row, 8).Value = export_ws.Cells(source_row, 13).Value # M -> H
                ws.Cells(dest_row, 9).Value = export_ws.Cells(source_row, 14).Value # N -> I
            
            # 关闭导出清单
            export_wb.Close(False)
            
            # 处理发票数据
            if invoice_data:
                log("\n处理发票数据:")
                log(f"发票类型: {invoice_data.get('InvoiceTypeOrg', '未知')}")
                log(f"金额: {invoice_data.get('AmountInFiguers', '未知')}")
                log(f"总金额: {invoice_data.get('TotalAmount', '未知')}")
                log(f"税率: {invoice_data.get('CommodityTaxRate', '未知')}")
                log(f"税额: {invoice_data.get('TotalTax', '未知')}")
                log(f"销售方: {invoice_data.get('SellerName', '未知')}")
                
                # 提取并填充发票类型 (J列)
                invoice_type = invoice_data.get('InvoiceTypeOrg', '')
                if '增值税专用发票' in invoice_type:
                    ws.Range(f"J{current_row}").Value = '增值税专用发票'
                
                # 提取并填充金额 (K列)
                amount = invoice_data.get('AmountInFiguers', '')
                if amount:
                    ws.Range(f"K{current_row}").Value = amount
                
                # 提取并填充总金额 (L列)
                total_amount = invoice_data.get('TotalAmount', '')
                if total_amount:
                    ws.Range(f"L{current_row}").Value = total_amount
                
                # 提取并填充税率 (M列)
                tax_rate = invoice_data.get('CommodityTaxRate', [])
                if tax_rate and isinstance(tax_rate, list) and len(tax_rate) > 0:
                    if isinstance(tax_rate[0], dict):
                        ws.Range(f"M{current_row}").Value = tax_rate[0].get('word', '')
                    else:
                        ws.Range(f"M{current_row}").Value = tax_rate[0]
                
                # 提取并填充税额 (N列)
                total_tax = invoice_data.get('TotalTax', '')
                if total_tax:
                    ws.Range(f"N{current_row}").Value = total_tax
                
                # 提取并填充销售方名称 (R列)
                seller_name = invoice_data.get('SellerName', '')
                if seller_name:
                    ws.Range(f"R{current_row}").Value = seller_name
                
                # 从备注中提取开户行和账号信息
                remarks = invoice_data.get('Remarks', '')
                if remarks:
                    # 提取开户行 (S列)
                    bank_start = remarks.find('销方开户银行:')
                    if bank_start != -1:
                        bank_end = remarks.find(';', bank_start)
                        if bank_end != -1:
                            bank = remarks[bank_start+7:bank_end]
                            ws.Range(f"S{current_row}").Value = bank
                    
                    # 提取账号 (T列)
                    account_start = remarks.find('银行账号:', bank_start)
                    if account_start != -1:
                        account_end = remarks.find(';', account_start)
                        if account_end != -1:
                            account = remarks[account_start+5:account_end]
                            ws.Range(f"T{current_row}").Value = account
                
                # 计算当前关键词的P和Q列公式
                p_range = ws.Range(f"P{current_row}:P{current_row+valid_row_count-1}")
                p_range.Merge()
                p_range.Formula = f"=L{current_row}-SUM(G{current_row}:G{current_row+valid_row_count-1})"
                
                q_range = ws.Range(f"Q{current_row}:Q{current_row+valid_row_count-1}")
                q_range.Merge()
                q_range.Formula = f"=N{current_row}-SUM(H{current_row}:H{current_row+valid_row_count-1})"
                
                # 计算O列的验证
                i_sum = sum(ws.Cells(row, 9).Value or 0 for row in range(current_row, current_row+valid_row_count))
                k_value = ws.Range(f"K{current_row}").Value
                
                o_range = ws.Range(f"O{current_row}:O{current_row+valid_row_count-1}")
                o_range.Merge()
                if abs(i_sum - float(k_value)) < 0.01:
                    o_range.Value = "是"
                else:
                    o_range.Value = "否"
                
                # 填入U列
                u_range = ws.Range(f"U{current_row}:U{current_row+valid_row_count-1}")
                u_range.Merge()
                u_range.Formula = f"=K{current_row}"
            
            # 合并当前关键词的J-U列单元格
            for col in range(10, 22):  # J列是10，U列是21
                merge_range = ws.Range(
                    ws.Cells(current_row, col),
                    ws.Cells(current_row + valid_row_count - 1, col)
                )
                merge_range.Merge()
            
            # 更新当前行位置
            current_row += valid_row_count
        
        # 在所有关键词处理完成后，处理合计行
        total_row = current_row  # 合计行在最后一行数据的下一行
        
        # 设置合计行的公式
        columns = {
            'G': 7, 'H': 8, 'I': 9, 'K': 11, 'L': 12, 
            'N': 14, 'P': 16, 'Q': 17, 'U': 21
        }
        
        # 在A列写入"合计"
        ws.Cells(total_row, 1).Value = "合计"
        
        # 为每个列设置合计公式
        for col_letter, col_num in columns.items():
            formula = f"=SUM({col_letter}5:{col_letter}{total_row-1})"
            ws.Cells(total_row, col_num).Formula = formula
            log(f"设置{col_letter}{total_row}单元格公式: {formula}")
        
        # 设置合计行格式
        total_range = ws.Range(f"A{total_row}:U{total_row}")  # 修改到U列
        total_range.Font.Bold = True
        total_range.Borders.LineStyle = 1
        total_range.Interior.ColorIndex = 15
        
        # 在所有数据处理完成后，处理表头
        try:
            # 收集F列的所有时间值
            dates = []
            for row in range(5, total_row):
                date_value = ws.Cells(row, 6).Value  # F列
                if date_value:
                    # 转换为字符串并移除.0
                    date_str = str(date_value).replace('.0', '')
                    if len(date_str) == 6:  # 确保格式为YYYYMM
                        dates.append(date_str)
            
            # 去重并排序
            unique_dates = sorted(set(dates))
            
            if not unique_dates:
                log("未找到有效的日期")
                return
            
            # 生成表头文本
            header_text = ""
            if len(unique_dates) == 1:
                # 单个月份
                year = unique_dates[0][:4]
                month = unique_dates[0][4:].zfill(2)
                header_text = f"{year}年{month}月揭东供电局付非居民光伏用户明细表"
            else:
                # 检查是否连续
                is_continuous = True
                for i in range(len(unique_dates)-1):
                    curr_date = int(unique_dates[i])
                    next_date = int(unique_dates[i+1])
                    # 检查是否连续（考虑跨年的情况）
                    if next_date - curr_date != 1:
                        is_continuous = False
                        break
                
                if is_continuous:
                    # 连续月份
                    start_year = unique_dates[0][:4]
                    start_month = unique_dates[0][4:].zfill(2)
                    end_year = unique_dates[-1][:4]
                    end_month = unique_dates[-1][4:].zfill(2)
                    header_text = f"{start_year}年{start_month}月-{end_year}年{end_month}月揭东供电局付非居民光伏用户明细表"
                else:
                    # 不连续月份
                    date_parts = []
                    for date in unique_dates:
                        year = date[:4]
                        month = date[4:].zfill(2)
                        date_parts.append(f"{year}年{month}月")
                    header_text = "、".join(date_parts) + "揭东供电局付非居民光伏用户明细表"
            
            # 更新表头
            # 先保存原有格式
            header_range = ws.Range("B1:U1")
            original_font = {
                'Name': header_range.Font.Name,
                'Size': header_range.Font.Size,
                'Bold': header_range.Font.Bold,
                'Color': header_range.Font.Color,
                'Alignment': header_range.HorizontalAlignment,
                'VerticalAlignment': header_range.VerticalAlignment
            }
            
            try:
                header_range.UnMerge()
            except:
                pass
            
            # 清除原有内容（但保留格式）
            header_range.ClearContents()
            
            # 填入新内容
            ws.Range("B1").Value = header_text
            
            # 重新合并单元格
            header_range.Merge()
            
            # 恢复原有格式
            header_range.Font.Name = original_font['Name']
            header_range.Font.Size = original_font['Size']
            header_range.Font.Bold = original_font['Bold']
            header_range.Font.Color = original_font['Color']
            header_range.HorizontalAlignment = original_font['Alignment']
            header_range.VerticalAlignment = original_font['VerticalAlignment']
            
            # 最终保存时也指定格式
            wb.SaveAs(
                Filename=new_template_path,
                FileFormat=51,  # 51 = xlsx格式
                CreateBackup=False
            )
            log(f"表头已更新为: {header_text}")
            
        except Exception as e:
            log(f"更新表头时出错: {str(e)}")
        
    except Exception as e:
        log(f"处理过程中出错：{str(e)}")
    finally:
        try:
            wb.Close()
            excel.Quit()
        except:
            pass

if __name__ == "__main__":
    window = InvoiceProcessWindow()
    window.run()
