#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试 - 检查程序卡在哪里
"""

import os
import time

def test_imports():
    """测试导入是否正常"""
    print("1. 测试基础导入...")
    try:
        import win32com.client
        print("  ✅ win32com.client 导入成功")
    except Exception as e:
        print(f"  ❌ win32com.client 导入失败: {e}")
    
    try:
        import fitz
        print("  ✅ fitz (PyMuPDF) 导入成功")
    except Exception as e:
        print(f"  ❌ fitz 导入失败: {e}")
    
    try:
        from PIL import Image
        print("  ✅ PIL 导入成功")
    except Exception as e:
        print(f"  ❌ PIL 导入失败: {e}")

def test_surya_import():
    """测试Surya OCR导入"""
    print("\n2. 测试Surya OCR导入...")
    try:
        print("  正在导入 surya.recognition...")
        start_time = time.time()
        from surya.recognition import RecognitionPredictor
        print(f"  ✅ RecognitionPredictor 导入成功 ({time.time()-start_time:.1f}秒)")
        
        print("  正在导入 surya.detection...")
        start_time = time.time()
        from surya.detection import DetectionPredictor
        print(f"  ✅ DetectionPredictor 导入成功 ({time.time()-start_time:.1f}秒)")
        
        return True
    except Exception as e:
        print(f"  ❌ Surya OCR 导入失败: {e}")
        return False

def test_surya_init():
    """测试Surya OCR初始化"""
    print("\n3. 测试Surya OCR初始化...")
    try:
        from surya.recognition import RecognitionPredictor
        from surya.detection import DetectionPredictor
        
        print("  正在初始化检测模型...")
        start_time = time.time()
        detection_predictor = DetectionPredictor()
        print(f"  ✅ 检测模型初始化成功 ({time.time()-start_time:.1f}秒)")
        
        print("  正在初始化识别模型...")
        start_time = time.time()
        recognition_predictor = RecognitionPredictor()
        print(f"  ✅ 识别模型初始化成功 ({time.time()-start_time:.1f}秒)")
        
        return True
    except Exception as e:
        print(f"  ❌ Surya OCR 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_access():
    """测试文件访问"""
    print("\n4. 测试文件访问...")
    
    files_to_check = [
        r"D:\vscode project\发票处理\1\模板.xls",
        r"D:\vscode project\发票处理\1\202501导出清单.xlsx",
        r"D:\vscode project\发票处理\1\202501顺洋、来宁、锦阳、奥捷、奥源-5张"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"  ✅ 文件存在: {os.path.basename(file_path)}")
        else:
            print(f"  ❌ 文件不存在: {file_path}")
    
    # 检查PDF文件
    invoice_folder = r"D:\vscode project\发票处理\1\202501顺洋、来宁、锦阳、奥捷、奥源-5张"
    if os.path.exists(invoice_folder):
        pdf_files = [f for f in os.listdir(invoice_folder) if f.lower().endswith('.pdf')]
        print(f"  ✅ 找到 {len(pdf_files)} 个PDF文件")
        for pdf in pdf_files[:3]:  # 只显示前3个
            print(f"    - {pdf}")
    else:
        print("  ❌ 发票文件夹不存在")

def test_pdf_conversion():
    """测试PDF转换"""
    print("\n5. 测试PDF转换...")
    try:
        import fitz
        
        invoice_folder = r"D:\vscode project\发票处理\1\202501顺洋、来宁、锦阳、奥捷、奥源-5张"
        if not os.path.exists(invoice_folder):
            print("  ❌ 发票文件夹不存在")
            return False
        
        pdf_files = [f for f in os.listdir(invoice_folder) if f.lower().endswith('.pdf')]
        if not pdf_files:
            print("  ❌ 未找到PDF文件")
            return False
        
        test_pdf = os.path.join(invoice_folder, pdf_files[0])
        print(f"  测试PDF: {os.path.basename(test_pdf)}")
        
        start_time = time.time()
        doc = fitz.open(test_pdf)
        page = doc[0]
        mat = fitz.Matrix(2.0, 2.0)  # 降低分辨率加快速度
        pix = page.get_pixmap(matrix=mat)
        
        temp_image = "test_conversion.png"
        pix.save(temp_image)
        doc.close()
        
        print(f"  ✅ PDF转换成功 ({time.time()-start_time:.1f}秒)")
        
        # 清理临时文件
        if os.path.exists(temp_image):
            os.remove(temp_image)
        
        return True
        
    except Exception as e:
        print(f"  ❌ PDF转换失败: {e}")
        return False

def main():
    """主测试函数"""
    print("快速测试 - 检查程序各个环节")
    print("="*50)
    
    # 测试各个环节
    test_imports()
    
    if test_surya_import():
        if test_surya_init():
            print("\n✅ Surya OCR 可以正常使用")
        else:
            print("\n❌ Surya OCR 初始化失败，可能需要下载模型")
    else:
        print("\n❌ Surya OCR 不可用")
    
    test_file_access()
    test_pdf_conversion()
    
    print("\n" + "="*50)
    print("测试完成")
    
    # 给出建议
    print("\n建议:")
    print("1. 如果Surya OCR初始化很慢，可能在下载模型，请耐心等待")
    print("2. 如果一直卡住，可以考虑使用EasyOCR版本")
    print("3. 您可以直接运行这个测试看具体卡在哪一步")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
