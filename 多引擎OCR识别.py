#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多引擎OCR识别系统 - 专门针对中文发票优化
"""

import os
import easyocr
import fitz
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import re

class MultiEngineOCR:
    def __init__(self):
        """初始化多引擎OCR系统"""
        print("正在初始化多引擎OCR系统...")
        
        # 初始化多个EasyOCR配置
        self.readers = []
        
        # 配置1：标准中英文识别
        try:
            reader1 = easyocr.Reader(['ch_sim', 'en'], gpu=False)
            self.readers.append(('标准中英文', reader1))
            print("✅ 标准中英文OCR初始化成功")
        except Exception as e:
            print(f"❌ 标准中英文OCR初始化失败: {e}")
        
        # 配置2：仅中文识别（更专注）
        try:
            reader2 = easyocr.Reader(['ch_sim'], gpu=False)
            self.readers.append(('仅中文', reader2))
            print("✅ 仅中文OCR初始化成功")
        except Exception as e:
            print(f"❌ 仅中文OCR初始化失败: {e}")
        
        print(f"多引擎OCR系统初始化完成，共{len(self.readers)}个引擎")
    
    def create_enhanced_images(self, image_path):
        """创建多种增强版本的图像"""
        enhanced_images = []
        
        try:
            # 读取原始图像
            img = cv2.imread(image_path)
            if img is None:
                return [image_path]
            
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            height, width = gray.shape
            
            # 方法1：超高分辨率版本（5倍放大）
            scale_factor = 5.0
            super_high_res = cv2.resize(gray, (int(width * scale_factor), int(height * scale_factor)), 
                                      interpolation=cv2.INTER_CUBIC)
            path1 = "enhanced_super_high_res.png"
            cv2.imwrite(path1, super_high_res)
            enhanced_images.append(path1)
            
            # 方法2：对比度和亮度优化
            enhanced = cv2.convertScaleAbs(super_high_res, alpha=2.0, beta=50)
            path2 = "enhanced_contrast_bright.png"
            cv2.imwrite(path2, enhanced)
            enhanced_images.append(path2)
            
            # 方法3：自适应直方图均衡化
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            clahe_img = clahe.apply(super_high_res)
            path3 = "enhanced_clahe.png"
            cv2.imwrite(path3, clahe_img)
            enhanced_images.append(path3)
            
            # 方法4：形态学操作（去噪和增强文字）
            kernel = np.ones((2,2), np.uint8)
            morph_img = cv2.morphologyEx(super_high_res, cv2.MORPH_CLOSE, kernel)
            path4 = "enhanced_morph.png"
            cv2.imwrite(path4, morph_img)
            enhanced_images.append(path4)
            
            # 方法5：双边滤波去噪
            bilateral = cv2.bilateralFilter(super_high_res, 9, 75, 75)
            path5 = "enhanced_bilateral.png"
            cv2.imwrite(path5, bilateral)
            enhanced_images.append(path5)
            
            print(f"创建了{len(enhanced_images)}个增强图像版本")
            return enhanced_images
            
        except Exception as e:
            print(f"图像增强失败: {e}")
            return [image_path]
    
    def recognize_with_all_engines(self, image_paths):
        """使用所有OCR引擎识别图像"""
        all_results = []
        
        for img_path in image_paths:
            print(f"\n处理图像: {os.path.basename(img_path)}")
            
            for engine_name, reader in self.readers:
                try:
                    print(f"  使用{engine_name}引擎识别...")
                    
                    # 使用不同的参数配置
                    configs = [
                        {'detail': 1, 'paragraph': False, 'width_ths': 0.7, 'height_ths': 0.7},
                        {'detail': 1, 'paragraph': False, 'width_ths': 0.5, 'height_ths': 0.5},
                        {'detail': 1, 'paragraph': True, 'width_ths': 0.8, 'height_ths': 0.8}
                    ]
                    
                    for i, config in enumerate(configs):
                        try:
                            result = reader.readtext(img_path, **config)
                            
                            all_results.append({
                                'image': img_path,
                                'engine': engine_name,
                                'config': i + 1,
                                'result': result,
                                'text_count': len(result)
                            })
                            
                            print(f"    配置{i+1}: 识别到{len(result)}个文本区域")
                            
                        except Exception as e:
                            print(f"    配置{i+1}失败: {e}")
                            
                except Exception as e:
                    print(f"  {engine_name}引擎失败: {e}")
        
        return all_results
    
    def find_best_matches(self, all_results, target_keywords):
        """找到每个关键词的最佳匹配"""
        best_matches = {}
        
        for keyword in target_keywords:
            candidates = []
            
            # 收集所有包含关键词的候选项
            for result_set in all_results:
                for bbox, text, confidence in result_set['result']:
                    if keyword in text:
                        candidates.append({
                            'text': text,
                            'confidence': confidence,
                            'image': result_set['image'],
                            'engine': result_set['engine'],
                            'config': result_set['config'],
                            'keyword': keyword
                        })
            
            # 按置信度排序，选择最佳匹配
            if candidates:
                best_candidate = max(candidates, key=lambda x: x['confidence'])
                best_matches[keyword] = best_candidate
            else:
                best_matches[keyword] = None
        
        return best_matches
    
    def correct_ocr_errors(self, text):
        """OCR错误修正字典"""
        corrections = {
            # 公司名称常见错误
            '奥捷': ['樊捷', '奧捷', '澳捷', '敖捷'],
            '奥源': ['樊源', '奧源', '澳源', '敖源'],
            '来宁': ['来亍', '來宁', '来寧', '來寧'],
            '锦阳': ['锦阴', '錦阳', '锦陽', '錦陽', '锦阴'],
            '顺洋': ['順洋', '顺羊', '順羊'],
            '揭阳': ['揭阴', '揭陽', '揭陰'],
            '广东': ['广氽', '廣东', '廣氽'],
            '天美': ['夭美', '天羙', '夭羙'],
            
            # 发票类型错误
            '增值税': ['僧值税', '橹徒税', '增僮税', '增值稅'],
            '专用发票': ['專用发票', '专用發票', '專用發票'],
            '普通发票': ['普通發票', '普遍发票', '普遍發票'],
            
            # 金额符号错误
            '￥': ['羊', '芏', '¥'],
            
            # 数字错误
            '13%': ['135', '130', '13'],
            '16%': ['165', '160', '16'],
            '17%': ['175', '170', '17'],
        }
        
        corrected_text = text
        for correct, errors in corrections.items():
            for error in errors:
                if error in corrected_text:
                    corrected_text = corrected_text.replace(error, correct)
                    print(f"OCR修正: '{error}' -> '{correct}'")
        
        return corrected_text
    
    def cleanup_temp_files(self, file_paths):
        """清理临时文件"""
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except:
                pass

def test_multi_engine_ocr():
    """测试多引擎OCR系统"""
    try:
        # 初始化多引擎OCR
        multi_ocr = MultiEngineOCR()
        
        # 查找PDF文件
        pdf_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(root, file))
        
        if not pdf_files:
            print("未找到PDF文件")
            return
        
        # 选择一个包含"奥捷"的PDF文件进行测试
        test_pdf = None
        for pdf in pdf_files:
            if '奥捷' in pdf:
                test_pdf = pdf
                break
        
        if not test_pdf:
            test_pdf = pdf_files[0]
        
        print(f"测试PDF文件: {test_pdf}")
        
        # PDF转图片
        doc = fitz.open(test_pdf)
        page = doc[0]
        mat = fitz.Matrix(4.0, 4.0)  # 4倍缩放
        pix = page.get_pixmap(matrix=mat)
        
        original_image = "original_test.png"
        pix.save(original_image)
        doc.close()
        
        # 创建增强图像
        enhanced_images = multi_ocr.create_enhanced_images(original_image)
        all_images = [original_image] + enhanced_images
        
        # 多引擎识别
        print("\n开始多引擎OCR识别...")
        all_results = multi_ocr.recognize_with_all_engines(all_images)
        
        # 查找目标关键词
        target_keywords = ['奥捷', '奥源', '来宁', '锦阳', '顺洋', '增值税专用发票', '13%']
        best_matches = multi_ocr.find_best_matches(all_results, target_keywords)
        
        print("\n" + "="*80)
        print("多引擎OCR识别结果:")
        print("="*80)
        
        for keyword, match in best_matches.items():
            if match:
                # 应用OCR错误修正
                corrected_text = multi_ocr.correct_ocr_errors(match['text'])
                
                print(f"\n关键词: '{keyword}'")
                print(f"  原始识别: '{match['text']}'")
                print(f"  修正后: '{corrected_text}'")
                print(f"  置信度: {match['confidence']:.3f}")
                print(f"  来源: {match['engine']} (配置{match['config']}) - {os.path.basename(match['image'])}")
            else:
                print(f"\n关键词: '{keyword}' - 未找到匹配")
        
        # 清理临时文件
        multi_ocr.cleanup_temp_files(all_images)
        print("\n临时文件已清理")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_multi_engine_ocr()
    input("\n按回车键退出...")
