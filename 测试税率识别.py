#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的税率识别功能
"""

import os
import easyocr
import fitz
import re

def test_tax_rate_recognition():
    """测试税率识别功能"""
    try:
        # 模拟OCR识别结果（基于实际调试数据）
        mock_ocr_result = [
            (None, '电子发票', 0.97),
            (None, '僧值税', 0.01),
            (None, '用发票)', 0.99),
            (None, '发票号码:  25442000000067784497', 0.69),
            (None, '开票日期:  2025年02月078', 0.62),
            (None, '-税)', 0.00),
            (None, '共1页  第1页', 0.43),
            (None, '购', 0.87),
            (None, '名称:广东电网有限责任公司揭阳供电局', 0.67),
            (None, '销', 0.98),
            (None, '名称:揭阳天芙新能源科技有限公司', 0.43),
            (None, '秀', 0.00),
            (None, '隽', 0.95),
            (None, '信', 0.80),
            (None, '统一社会信用代码/纳税人识别号:91445200190412515J', 0.66),
            (None, '信', 0.68),
            (None, '统一社会信用代码/纳税人识别号:91445202UAC00DE238', 0.24),
            (None, '项目名称', 0.74),
            (None, '规格型号', 0.26),
            (None, '单  位', 0.58),
            (None, '数', 0.77),
            (None, '量', 0.84),
            (None, '税率/征收率', 0.20),  # 第23行
            (None, '税', 1.00),
            (None, '|发电*光伏发电', 0.15),
            (None, '干瓦时', 0.29),
            (None, '5278620.4008851177012', 0.88),
            (None, '211612.02', 0.68),
            (None, '135', 0.43),  # 第29行 - 这应该被识别为13%
            (None, '27509.55', 0.55),
            (None, '芏211612.02', 0.26),
            (None, '芏27509.5', 0.27),
            (None, '价税合计 (大写)', 0.76),
            (None, ')贰拾叁万玖仟壹佰贰拾壹圆伍角柒分', 0.36),
            (None, '(小写)羊239121.57', 0.27),
            (None, '购方开户银行:中国工商银行股份有限公司揭阳分行营业部;', 0.74),
            (None, '银行账号:2019002129200505667;', 0.99),
            (None, '销方开户银行:华夏银行北京国贯支行;', 0.71),
            (None, '银行账号:10263000001365419;', 0.99),
            (None, '揭阳天美新能源科技有限公司', 0.84),
            (None, '2024年11月', 0.98),
            (None, '揭东供电局', 0.98),
            (None, '开票人:  崔西宁', 0.61),
            (None, '广奈', 0.04)
        ]
        
        print("测试税率识别功能...")
        print("模拟OCR结果中的关键行:")
        print("第23行: '税率/征收率'")
        print("第29行: '135' (应该被识别为13%)")
        print()
        
        # 测试税率识别逻辑
        result = {'CommodityTaxRate': ''}
        all_text = [(text, confidence) for _, text, confidence in mock_ocr_result]
        
        for i, (line, confidence) in enumerate(all_text):
            line = str(line).strip()
            
            # 税率识别逻辑
            if not result['CommodityTaxRate']:
                # 查找税率相关的行
                if '税率' in line or '征收率' in line:
                    print(f"找到税率相关行: {line}")
                    # 在税率行或附近查找百分比
                    tax_rate_patterns = [
                        r'(\d+(?:\.\d+)?)%',
                        r'税率.*?(\d+(?:\.\d+)?)%',
                        r'征收率.*?(\d+(?:\.\d+)?)%'
                    ]
                    for pattern in tax_rate_patterns:
                        tax_rate_match = re.search(pattern, line)
                        if tax_rate_match:
                            rate_str = tax_rate_match.group(1)
                            result['CommodityTaxRate'] = [rate_str + '%']
                            print(f"✅ 识别税率: {line} -> {rate_str}%")
                            break
                
                # 处理OCR错误的特殊情况
                elif line.strip() in ['135', '13', '130']:
                    print(f"找到可能的税率数字: {line}")
                    # 检查上下文是否有税率相关信息
                    context_has_tax = False
                    print(f"  检查上下文范围: {max(0, i-5)} 到 {min(len(all_text), i+6)}")
                    for j in range(max(0, i-5), min(len(all_text), i+6)):
                        if j != i:
                            context_line = all_text[j][0]
                            print(f"    检查第{j}行: '{context_line}'")
                            if '税率' in context_line or '征收率' in context_line:
                                context_has_tax = True
                                print(f"  ✅ 上下文中找到税率相关: {context_line}")
                                break

                    if not context_has_tax:
                        print(f"  ❌ 上下文中未找到税率相关信息")
                    
                    if context_has_tax:
                        if line.strip() == '135':
                            result['CommodityTaxRate'] = ['13%']
                            print(f"✅ 识别税率(修正OCR错误): {line} -> 13%")
                        elif line.strip() == '13':
                            result['CommodityTaxRate'] = ['13%']
                            print(f"✅ 识别税率: {line} -> 13%")
                        elif line.strip() == '130':
                            result['CommodityTaxRate'] = ['13%']
                            print(f"✅ 识别税率(修正OCR错误): {line} -> 13%")
        
        print()
        print("最终税率识别结果:")
        print(f"CommodityTaxRate: {result['CommodityTaxRate']}")
        
        if result['CommodityTaxRate'] == ['13%']:
            print("🎉 税率识别测试通过！")
            return True
        else:
            print("❌ 税率识别测试失败！")
            return False
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_tax_rate_recognition()
    input("\n按回车键退出...")
