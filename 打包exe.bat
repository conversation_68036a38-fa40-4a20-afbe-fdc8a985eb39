@echo off
echo 开始打包发票处理程序为exe文件...
echo.

REM 激活虚拟环境
call venv\Scripts\activate.bat

REM 检查是否成功激活虚拟环境
if "%VIRTUAL_ENV%"=="" (
    echo 错误：无法激活虚拟环境
    pause
    exit /b 1
)

echo 虚拟环境已激活: %VIRTUAL_ENV%
echo.

REM 创建dist目录
if not exist "dist" mkdir dist

echo 正在使用PyInstaller打包程序...
echo 这可能需要几分钟时间，请耐心等待...
echo.

REM 使用PyInstaller打包为单个exe文件
pyinstaller --onefile ^
    --windowed ^
    --name "发票处理程序" ^
    --icon=icon.ico ^
    --add-data "venv\Lib\site-packages\easyocr\model;easyocr\model" ^
    --hidden-import=easyocr ^
    --hidden-import=fitz ^
    --hidden-import=PyMuPDF ^
    --hidden-import=cv2 ^
    --hidden-import=PIL ^
    --hidden-import=torch ^
    --hidden-import=torchvision ^
    --hidden-import=numpy ^
    --hidden-import=requests ^
    --hidden-import=win32com.client ^
    --collect-all=easyocr ^
    --collect-all=torch ^
    --collect-all=torchvision ^
    "fppl-21.尝试替代百度识别.py"

if errorlevel 1 (
    echo.
    echo 打包失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo 打包完成！
echo 可执行文件位置: dist\发票处理程序.exe
echo.

REM 检查文件是否存在
if exist "dist\发票处理程序.exe" (
    echo 文件大小:
    dir "dist\发票处理程序.exe" | find ".exe"
    echo.
    echo 您可以将此exe文件复制到其他电脑运行
) else (
    echo 警告：未找到生成的exe文件
)

pause
