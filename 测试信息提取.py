#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的发票信息提取功能
"""

import os
import easyocr
import fitz
import re

def extract_invoice_info_test(text_list):
    """测试版本的发票信息提取函数"""
    result = {
        'InvoiceTypeOrg': '',
        'AmountInFiguers': '',
        'TotalAmount': '',
        'CommodityTaxRate': '',
        'TotalTax': '',
        'SellerName': '',
        'Remarks': ''
    }
    
    # 将所有识别的文本保存，包括低置信度的
    all_text = []
    for bbox, text, confidence in text_list:
        all_text.append((text.strip(), confidence))
    
    print("开始提取发票信息...")
    
    # 遍历所有文本进行信息提取
    for i, (line, confidence) in enumerate(all_text):
        line = str(line).strip()
        
        # 发票类型 - 处理OCR识别错误
        if ('增值税' in line or '僧值税' in line or '电子发票' in line) and not result['InvoiceTypeOrg']:
            if '专用' in line:
                result['InvoiceTypeOrg'] = '增值税专用发票'
            elif '普通' in line or '电子发票' in line:
                result['InvoiceTypeOrg'] = '增值税普通发票'
            print(f"识别发票类型: {line} -> {result['InvoiceTypeOrg']}")
        
        # 价税合计 - 查找小写金额
        if ('小写' in line or '羊' in line or '￥' in line) and not result['TotalAmount']:
            # 处理OCR错误：羊 -> ￥
            line_fixed = line.replace('羊', '￥').replace('芏', '￥')
            total_patterns = [
                r'小写.*?￥?(\d+\.?\d*)',
                r'￥(\d+\.?\d*)',
                r'(\d+\.\d{2})'  # 直接匹配金额格式
            ]
            for pattern in total_patterns:
                total_match = re.search(pattern, line_fixed)
                if total_match:
                    total_str = total_match.group(1)
                    if '.' in total_str and len(total_str.split('.')[1]) <= 2:
                        result['TotalAmount'] = total_str
                        result['AmountInFiguers'] = total_str  # 小写金额同时作为金额
                        print(f"识别价税合计: {line} -> {total_str}")
                        break
        
        # 税额 - 查找税额数字
        if not result['TotalTax']:
            # 查找可能的税额（通常是较大的数字，小于总金额）
            tax_patterns = [
                r'芏(\d+\.?\d*)',  # OCR错误：芏 -> ￥
                r'税.*?(\d+\.?\d*)',
                r'^(\d{4,}\.\d{2})$'  # 独立的金额数字
            ]
            for pattern in tax_patterns:
                tax_match = re.search(pattern, line)
                if tax_match:
                    tax_str = tax_match.group(1)
                    # 验证是否为合理的税额
                    try:
                        tax_amount = float(tax_str)
                        if result['TotalAmount']:
                            total_amount = float(result['TotalAmount'])
                            if 0 < tax_amount < total_amount * 0.5:
                                result['TotalTax'] = tax_str
                                print(f"识别税额: {line} -> {tax_str}")
                                break
                        elif 1000 < tax_amount < 100000:  # 合理的税额范围
                            result['TotalTax'] = tax_str
                            print(f"识别税额: {line} -> {tax_str}")
                            break
                    except:
                        pass
        
        # 税率 - 查找税率信息
        if not result['CommodityTaxRate']:
            # 处理常见的税率格式
            tax_rate_patterns = [
                r'(\d+)%',
                r'^(\d{1,2})$'  # 单独的数字，可能是税率
            ]
            for pattern in tax_rate_patterns:
                tax_rate_match = re.search(pattern, line)
                if tax_rate_match:
                    rate_num = tax_rate_match.group(1)
                    # 验证是否为合理的税率
                    try:
                        rate_val = int(rate_num)
                        if rate_val in [0, 3, 6, 9, 13, 16, 17]:  # 常见税率
                            result['CommodityTaxRate'] = [rate_num + '%']
                            print(f"识别税率: {line} -> {rate_num}%")
                            break
                        elif rate_val == 135:  # OCR错误：135 -> 13%
                            result['CommodityTaxRate'] = ['13%']
                            print(f"识别税率: {line} -> 13% (修正135)")
                            break
                    except:
                        pass

            # 如果还没找到税率，根据税额和总金额计算
            if not result['CommodityTaxRate'] and result['TotalTax'] and result['TotalAmount']:
                try:
                    tax_amount = float(result['TotalTax'])
                    total_amount = float(result['TotalAmount'])
                    net_amount = total_amount - tax_amount
                    if net_amount > 0:
                        calculated_rate = (tax_amount / net_amount) * 100
                        # 匹配到最接近的标准税率
                        standard_rates = [0, 3, 6, 9, 13, 16, 17]
                        closest_rate = min(standard_rates, key=lambda x: abs(x - calculated_rate))
                        if abs(closest_rate - calculated_rate) < 2:  # 误差在2%以内
                            result['CommodityTaxRate'] = [f'{closest_rate}%']
                            print(f"计算税率: {calculated_rate:.1f}% -> {closest_rate}%")
                except:
                    pass
        
        # 销售方名称 - 查找销售方
        if '销' in line and '名称' in line and not result['SellerName']:
            # 查找下一行或当前行的公司名称
            seller_patterns = [
                r'名称[:：]\s*(.+)',
                r'销.*?名称.*?[:：]\s*(.+)'
            ]
            for pattern in seller_patterns:
                seller_match = re.search(pattern, line)
                if seller_match:
                    seller_name = seller_match.group(1).strip()
                    if len(seller_name) > 3:
                        result['SellerName'] = seller_name
                        print(f"识别销售方: {line} -> {seller_name}")
                        break

            # 如果当前行没找到，查找下一行
            if not result['SellerName'] and i + 1 < len(all_text):
                next_line = all_text[i + 1][0]
                if '公司' in next_line or '有限' in next_line:
                    result['SellerName'] = next_line.strip()
                    print(f"识别销售方(下一行): {next_line}")

        # 如果还没找到销售方，查找包含公司名称的行
        if not result['SellerName'] and ('公司' in line or '有限' in line) and len(line) > 5:
            # 排除购方信息
            if '购' not in line and '电网' not in line and '供电局' not in line:
                # 常见的销售方公司名称模式
                if any(keyword in line for keyword in ['科技', '新能源', '实业', '贸易', '工程', '建设']):
                    result['SellerName'] = line.strip()
                    print(f"识别销售方(公司名): {line}")
        
        # 备注信息（开户行和账号）
        if '开户行' in line or '账号' in line or '银行' in line:
            result['Remarks'] = result['Remarks'] + line + ';'
    
    return result

def test_invoice_extraction():
    """测试发票信息提取"""
    try:
        # 初始化EasyOCR
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        
        # 查找PDF文件
        pdf_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(root, file))
        
        if not pdf_files:
            print("未找到PDF文件")
            return
        
        test_pdf = pdf_files[0]
        print(f"测试PDF文件: {test_pdf}")
        
        # PDF转图片
        doc = fitz.open(test_pdf)
        page = doc[0]
        mat = fitz.Matrix(2.0, 2.0)
        pix = page.get_pixmap(matrix=mat)
        
        temp_image = "test_extraction.png"
        pix.save(temp_image)
        doc.close()
        
        print("开始OCR识别...")
        result = reader.readtext(temp_image)
        
        print(f"OCR识别到 {len(result)} 个文本区域")
        
        # 测试信息提取
        print("\n" + "="*60)
        invoice_data = extract_invoice_info_test(result)
        
        print("\n最终提取结果:")
        print("="*60)
        for key, value in invoice_data.items():
            print(f"{key}: {value}")
        
        # 清理临时文件
        try:
            os.remove(temp_image)
        except:
            pass
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_invoice_extraction()
    input("\n按回车键退出...")
