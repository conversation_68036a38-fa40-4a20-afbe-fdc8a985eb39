# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['fppl-21.尝试替代百度识别.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['easyocr', 'fitz', 'cv2', 'PIL', 'torch', 'numpy', 'requests', 'win32com.client', 'tkinter', 'tkinter.filedialog', 'tkinter.ttk', 'tkinter.messagebox'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='InvoiceProcessor_v2',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
