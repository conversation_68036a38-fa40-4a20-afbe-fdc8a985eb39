# 发票处理程序 - 故障排除指南

## 问题解决方案

### ✅ 已解决：PDF处理问题

**问题描述**：
```
PDFInfoNotInstalledError: Unable to get page count. Is poppler installed and in PATH?
```

**解决方案**：
程序已更新为使用PyMuPDF替代pdf2image+Poppler，无需额外配置Poppler工具。

**技术细节**：
- 原来：pdf2image + Poppler（需要外部工具）
- 现在：PyMuPDF（纯Python库，无需外部依赖）

### 常见问题及解决方案

#### 1. 虚拟环境问题

**问题**：模块导入错误
```
ModuleNotFoundError: No module named 'easyocr'
```

**解决方案**：
```bash
# 确保在虚拟环境中运行
.\venv\Scripts\activate
python "fppl-21.尝试替代百度识别.py"
```

#### 2. OCR识别结果为空

**可能原因**：
- 图片质量太差
- 文字太小或模糊
- 图片格式不支持

**解决方案**：
- 使用高分辨率的发票图片
- 确保文字清晰可见
- 支持的格式：PDF、JPG、PNG、BMP等

#### 3. 识别准确率低

**优化建议**：
- 使用原始高质量发票文件
- 避免扫描件的二次压缩
- 确保发票文字水平放置

#### 4. 程序运行缓慢

**原因**：使用CPU进行OCR识别

**优化方案**：
- 耐心等待处理完成
- 考虑升级硬件（更快的CPU）
- 批量处理时分批进行

#### 5. 内存不足

**症状**：程序崩溃或卡死

**解决方案**：
- 关闭其他占用内存的程序
- 重启计算机释放内存
- 一次处理较少的发票文件

### 测试工具

#### 1. 基础功能测试
```bash
python "测试EasyOCR.py"
```

#### 2. PDF处理测试
```bash
python "测试PDF处理.py"
```

### 日志分析

#### 正常启动日志
```
使用PyMuPDF处理PDF文件
正在初始化EasyOCR...
Using CPU. Note: This module is much faster with a GPU.
EasyOCR初始化完成！
```

#### PDF处理日志
```
开始处理发票: xxx.pdf
正在转换PDF为图片...
PyMuPDF转换完成: temp_invoice_pymupdf.png
PDF转换完成
正在进行OCR识别...
OCR识别到 X 个文本区域
```

#### 识别结果日志
```
识别结果: {
    'InvoiceTypeOrg': '增值税专用发票',
    'AmountInFiguers': '1234.56',
    'TotalAmount': '1234.56',
    ...
}
```

### 性能优化建议

1. **硬件要求**：
   - CPU：4核心以上
   - 内存：8GB以上
   - 存储：SSD硬盘

2. **软件优化**：
   - 关闭不必要的后台程序
   - 使用最新版本的程序
   - 定期清理临时文件

3. **使用技巧**：
   - 批量处理时分组进行
   - 优先处理高质量的发票文件
   - 定期检查识别结果的准确性

### 联系支持

如果遇到其他问题：

1. **检查日志**：查看程序输出的详细错误信息
2. **运行测试**：使用提供的测试脚本验证功能
3. **环境检查**：确保在正确的虚拟环境中运行
4. **文件检查**：确保发票文件完整且可读

### 更新记录

- **v2.0**：使用PyMuPDF替代Poppler，解决PDF处理问题
- **v1.0**：使用EasyOCR替代百度API，实现本地化处理
